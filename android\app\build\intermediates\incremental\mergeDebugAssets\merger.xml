<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="com.appsflyer:af-android-sdk:6.16.2" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd925e0837d751f1c243deaf71e9dd3\transformed\af-android-sdk-6.16.2\assets"><file name="com/appsflyer/internal/69ac8441c455403eb-" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd925e0837d751f1c243deaf71e9dd3\transformed\af-android-sdk-6.16.2\assets\com\appsflyer\internal\69ac8441c455403eb-"/><file name="com/appsflyer/internal/70d3699e49ffe95ac-" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd925e0837d751f1c243deaf71e9dd3\transformed\af-android-sdk-6.16.2\assets\com\appsflyer\internal\70d3699e49ffe95ac-"/><file name="com/appsflyer/internal/a7f432bc3983c79d-" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd925e0837d751f1c243deaf71e9dd3\transformed\af-android-sdk-6.16.2\assets\com\appsflyer\internal\a7f432bc3983c79d-"/><file name="com/appsflyer/internal/b6c922fccc5244a3a-" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\4bd925e0837d751f1c243deaf71e9dd3\transformed\af-android-sdk-6.16.2\assets\com\appsflyer\internal\b6c922fccc5244a3a-"/></source></dataSet><dataSet config="com.google.mlkit:barcode-scanning:17.2.0" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f96d8edf483d24c0f1083f99124655b0\transformed\barcode-scanning-17.2.0\assets"><file name="mlkit_barcode_models/barcode_ssd_mobilenet_v1_dmp25_quant.tflite" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f96d8edf483d24c0f1083f99124655b0\transformed\barcode-scanning-17.2.0\assets\mlkit_barcode_models\barcode_ssd_mobilenet_v1_dmp25_quant.tflite"/><file name="mlkit_barcode_models/oned_auto_regressor_mobile.tflite" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f96d8edf483d24c0f1083f99124655b0\transformed\barcode-scanning-17.2.0\assets\mlkit_barcode_models\oned_auto_regressor_mobile.tflite"/><file name="mlkit_barcode_models/oned_feature_extractor_mobile.tflite" path="C:\Users\<USER>\.gradle\caches\8.10.2\transforms\f96d8edf483d24c0f1083f99124655b0\transformed\barcode-scanning-17.2.0\assets\mlkit_barcode_models\oned_feature_extractor_mobile.tflite"/></source></dataSet><dataSet config=":expo-modules-core" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\inpress-react-native\node_modules\expo-modules-core\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo-updates-interface" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\inpress-react-native\node_modules\expo-updates-interface\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo-dev-menu-interface" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\inpress-react-native\node_modules\expo-dev-menu-interface\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo-web-browser" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\inpress-react-native\node_modules\expo-web-browser\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo-tracking-transparency" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\inpress-react-native\node_modules\expo-tracking-transparency\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo-system-ui" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\inpress-react-native\node_modules\expo-system-ui\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo-splash-screen" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\inpress-react-native\node_modules\expo-splash-screen\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo-sms" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\inpress-react-native\node_modules\expo-sms\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo-sharing" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\inpress-react-native\node_modules\expo-sharing\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo-notifications" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\inpress-react-native\node_modules\expo-notifications\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo-media-library" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\inpress-react-native\node_modules\expo-media-library\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo-location" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\inpress-react-native\node_modules\expo-location\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo-localization" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\inpress-react-native\node_modules\expo-localization\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo-linking" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\inpress-react-native\node_modules\expo-linking\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo-linear-gradient" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\inpress-react-native\node_modules\expo-linear-gradient\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo-keep-awake" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\inpress-react-native\node_modules\expo-keep-awake\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo-json-utils" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\inpress-react-native\node_modules\expo-json-utils\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo-manifests" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\inpress-react-native\node_modules\expo-manifests\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo-image-picker" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\inpress-react-native\node_modules\expo-image-picker\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo-image-manipulator" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\inpress-react-native\node_modules\expo-image-manipulator\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo-image-loader" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\inpress-react-native\node_modules\expo-image-loader\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo-image" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\inpress-react-native\node_modules\expo-image\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo-font" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\inpress-react-native\node_modules\expo-font\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo-file-system" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\inpress-react-native\node_modules\expo-file-system\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo-device" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\inpress-react-native\node_modules\expo-device\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo-dev-menu" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\inpress-react-native\node_modules\expo-dev-menu\android\build\intermediates\library_assets\debug\packageDebugAssets\out"><file name="dev-menu-packager-host" path="C:\inpress-react-native\node_modules\expo-dev-menu\android\build\intermediates\library_assets\debug\packageDebugAssets\out\dev-menu-packager-host"/><file name="EXDevMenuApp.android.js" path="C:\inpress-react-native\node_modules\expo-dev-menu\android\build\intermediates\library_assets\debug\packageDebugAssets\out\EXDevMenuApp.android.js"/><file name="Inter-Black.otf" path="C:\inpress-react-native\node_modules\expo-dev-menu\android\build\intermediates\library_assets\debug\packageDebugAssets\out\Inter-Black.otf"/><file name="Inter-Bold.otf" path="C:\inpress-react-native\node_modules\expo-dev-menu\android\build\intermediates\library_assets\debug\packageDebugAssets\out\Inter-Bold.otf"/><file name="Inter-ExtraBold.otf" path="C:\inpress-react-native\node_modules\expo-dev-menu\android\build\intermediates\library_assets\debug\packageDebugAssets\out\Inter-ExtraBold.otf"/><file name="Inter-ExtraLight.otf" path="C:\inpress-react-native\node_modules\expo-dev-menu\android\build\intermediates\library_assets\debug\packageDebugAssets\out\Inter-ExtraLight.otf"/><file name="Inter-Light.otf" path="C:\inpress-react-native\node_modules\expo-dev-menu\android\build\intermediates\library_assets\debug\packageDebugAssets\out\Inter-Light.otf"/><file name="Inter-Medium.otf" path="C:\inpress-react-native\node_modules\expo-dev-menu\android\build\intermediates\library_assets\debug\packageDebugAssets\out\Inter-Medium.otf"/><file name="Inter-Regular.otf" path="C:\inpress-react-native\node_modules\expo-dev-menu\android\build\intermediates\library_assets\debug\packageDebugAssets\out\Inter-Regular.otf"/><file name="Inter-SemiBold.otf" path="C:\inpress-react-native\node_modules\expo-dev-menu\android\build\intermediates\library_assets\debug\packageDebugAssets\out\Inter-SemiBold.otf"/><file name="Inter-Thin.otf" path="C:\inpress-react-native\node_modules\expo-dev-menu\android\build\intermediates\library_assets\debug\packageDebugAssets\out\Inter-Thin.otf"/></source></dataSet><dataSet config=":expo-dev-launcher" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\inpress-react-native\node_modules\expo-dev-launcher\android\build\intermediates\library_assets\debug\packageDebugAssets\out"><file name="expo_dev_launcher_android.bundle" path="C:\inpress-react-native\node_modules\expo-dev-launcher\android\build\intermediates\library_assets\debug\packageDebugAssets\out\expo_dev_launcher_android.bundle"/><file name="Inter-Black.otf" path="C:\inpress-react-native\node_modules\expo-dev-launcher\android\build\intermediates\library_assets\debug\packageDebugAssets\out\Inter-Black.otf"/><file name="Inter-Bold.otf" path="C:\inpress-react-native\node_modules\expo-dev-launcher\android\build\intermediates\library_assets\debug\packageDebugAssets\out\Inter-Bold.otf"/><file name="Inter-ExtraBold.otf" path="C:\inpress-react-native\node_modules\expo-dev-launcher\android\build\intermediates\library_assets\debug\packageDebugAssets\out\Inter-ExtraBold.otf"/><file name="Inter-ExtraLight.otf" path="C:\inpress-react-native\node_modules\expo-dev-launcher\android\build\intermediates\library_assets\debug\packageDebugAssets\out\Inter-ExtraLight.otf"/><file name="Inter-Light.otf" path="C:\inpress-react-native\node_modules\expo-dev-launcher\android\build\intermediates\library_assets\debug\packageDebugAssets\out\Inter-Light.otf"/><file name="Inter-Medium.otf" path="C:\inpress-react-native\node_modules\expo-dev-launcher\android\build\intermediates\library_assets\debug\packageDebugAssets\out\Inter-Medium.otf"/><file name="Inter-Regular.otf" path="C:\inpress-react-native\node_modules\expo-dev-launcher\android\build\intermediates\library_assets\debug\packageDebugAssets\out\Inter-Regular.otf"/><file name="Inter-SemiBold.otf" path="C:\inpress-react-native\node_modules\expo-dev-launcher\android\build\intermediates\library_assets\debug\packageDebugAssets\out\Inter-SemiBold.otf"/><file name="Inter-Thin.otf" path="C:\inpress-react-native\node_modules\expo-dev-launcher\android\build\intermediates\library_assets\debug\packageDebugAssets\out\Inter-Thin.otf"/></source></dataSet><dataSet config=":expo-dev-client" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\inpress-react-native\node_modules\expo-dev-client\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo-crypto" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\inpress-react-native\node_modules\expo-crypto\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo-contacts" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\inpress-react-native\node_modules\expo-contacts\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo-constants" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\inpress-react-native\node_modules\expo-constants\android\build\intermediates\library_assets\debug\packageDebugAssets\out"><file name="app.config" path="C:\inpress-react-native\node_modules\expo-constants\android\build\intermediates\library_assets\debug\packageDebugAssets\out\app.config"/></source></dataSet><dataSet config=":expo-camera" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\inpress-react-native\node_modules\expo-camera\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo-blur" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\inpress-react-native\node_modules\expo-blur\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo-av" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\inpress-react-native\node_modules\expo-av\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo-asset" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\inpress-react-native\node_modules\expo-asset\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo-application" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\inpress-react-native\node_modules\expo-application\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-view-shot" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\inpress-react-native\node_modules\react-native-view-shot\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-svg" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\inpress-react-native\node_modules\react-native-svg\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-reanimated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\inpress-react-native\node_modules\react-native-reanimated\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-purchases" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\inpress-react-native\node_modules\react-native-purchases\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-maps" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\inpress-react-native\node_modules\react-native-maps\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-fbsdk-next" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\inpress-react-native\node_modules\react-native-fbsdk-next\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-appsflyer" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\inpress-react-native\node_modules\react-native-appsflyer\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":mixpanel-react-native" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\inpress-react-native\node_modules\mixpanel-react-native\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":stream-io_flat-list-mvcp" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\inpress-react-native\node_modules\@stream-io\flat-list-mvcp\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":sentry_react-native" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\inpress-react-native\node_modules\@sentry\react-native\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-picker_picker" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\inpress-react-native\node_modules\@react-native-picker\picker\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-community_netinfo" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\inpress-react-native\node_modules\@react-native-community\netinfo\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-community_datetimepicker" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\inpress-react-native\node_modules\@react-native-community\datetimepicker\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-async-storage_async-storage" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\inpress-react-native\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-webview" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\inpress-react-native\node_modules\react-native-webview\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-screens" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\inpress-react-native\node_modules\react-native-screens\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-safe-area-context" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\inpress-react-native\node_modules\react-native-safe-area-context\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-gesture-handler" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\inpress-react-native\node_modules\react-native-gesture-handler\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":lottie-react-native" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\inpress-react-native\node_modules\lottie-react-native\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\inpress-react-native\node_modules\expo\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\inpress-react-native\android\app\src\main\assets"><file name="fonts/InterTight-Regular.ttf" path="C:\inpress-react-native\android\app\src\main\assets\fonts\InterTight-Regular.ttf"/><file name="fonts/InterTight-SemiBold.ttf" path="C:\inpress-react-native\android\app\src\main\assets\fonts\InterTight-SemiBold.ttf"/><file name="fonts/PPEditorialOld-Italic.otf" path="C:\inpress-react-native\android\app\src\main\assets\fonts\PPEditorialOld-Italic.otf"/><file name="fonts/PPEditorialOld-Regular.otf" path="C:\inpress-react-native\android\app\src\main\assets\fonts\PPEditorialOld-Regular.otf"/></source></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\inpress-react-native\android\app\src\debug\assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\inpress-react-native\android\app\build\intermediates\shader_assets\debug\compileDebugShaders\out"/></dataSet></merger>