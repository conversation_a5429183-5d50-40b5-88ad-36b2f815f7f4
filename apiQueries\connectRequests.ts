import { get, post } from "@/network"
import { INPRESS_API_URL } from "./constants"
import { ConnectionMode } from "@/components/signInOrUp/ConnectionModeStep"
import { convertRawUser, RawUser, User } from "@/types/user"

type ContactStatus = {
  showAsExisting: boolean
  requestSent: boolean
  connected: boolean
}

type StatusByContactHash = Record<string, ContactStatus>

interface CheckContactStatusesParams {
  token: string
  phoneHashes: string[]
}

const checkContactStatuses = async ({
  token,
  phoneHashes,
}: CheckContactStatusesParams): Promise<StatusByContactHash> => {
  return post<{ phoneHashes: string[] }, StatusByContactHash>(
    `${INPRESS_API_URL}/check-contact-statuses`,
    { phoneHashes },
    token,
  )
}

interface CreateConnectRequestsProps {
  token: string
  phoneNumbers: string[]
}

interface CreateConnectRequestsParams {
  requestee_phone_number: string
  connection_mode: ConnectionMode
}

export const createConnectRequests = async ({
  token,
  phoneNumbers,
}: CreateConnectRequestsProps) => {
  await Promise.all(
    phoneNumbers.map((phoneNumber) =>
      post<CreateConnectRequestsParams, void>(
        `${INPRESS_API_URL}/create-connect-request`,
        {
          requestee_phone_number: phoneNumber,
          connection_mode: ConnectionMode.Friends,
        },
        token,
      ),
    ),
  )
}

interface CreateConnectRequestParams {
  requestee_id: number
  connection_mode: ConnectionMode
}

const createConnectRequestForUserId = async ({
  token,
  otherUserId,
}: {
  token: string
  otherUserId: number
}) => {
  await post<CreateConnectRequestParams, void>(
    `${INPRESS_API_URL}/create-connect-request`,
    { requestee_id: otherUserId, connection_mode: ConnectionMode.Friends },
    token,
  )
}

export enum ConnectRequestStatus {
  PENDING = "pending",
  ACCEPTED = "accepted",
}

export type NotificationConnectRequest = {
  id: number
  connectionMode: ConnectionMode
  status: ConnectRequestStatus
}

export interface RawNotificationConnectRequest
  extends Omit<NotificationConnectRequest, "connection_mode"> {
  connection_mode: ConnectionMode
}

type GetConnectRequestsParams = {
  connection_mode: ConnectionMode
}

type ConnectRequest = {
  id: number
  requester: User | null
  requestee: User | null
}

interface RawConnectRequest {
  id: number
  requester: RawUser | undefined
  requestee: RawUser | undefined
}

const getConnectRequests = async (token: string): Promise<ConnectRequest[]> => {
  const rawRequests = await get<GetConnectRequestsParams, RawConnectRequest[]>(
    `${INPRESS_API_URL}/connect-requests`,
    token,
    { connection_mode: ConnectionMode.Friends },
  )
  return rawRequests.map((rawRequest) => ({
    ...rawRequest,
    requester: rawRequest.requester
      ? convertRawUser(rawRequest.requester)
      : null,
    requestee: rawRequest.requestee
      ? convertRawUser(rawRequest.requestee)
      : null,
  }))
}

type ConnectRequestResponse = "accept" | "decline"

type RespondToConnectRequestParams = {
  connect_request_id: number
  response: ConnectRequestResponse
}

const respondToConnectRequest = async ({
  token,
  connectRequestId,
  response,
}: {
  token: string
  connectRequestId: number
  response: ConnectRequestResponse
}) => {
  await post<RespondToConnectRequestParams, void>(
    `${INPRESS_API_URL}/respond-to-connect-request`,
    { connect_request_id: connectRequestId, response },
    token,
  )
}

export {
  ContactStatus,
  StatusByContactHash,
  checkContactStatuses,
  createConnectRequestForUserId,
  getConnectRequests,
  ConnectRequest,
  ConnectRequestResponse,
  respondToConnectRequest,
}
