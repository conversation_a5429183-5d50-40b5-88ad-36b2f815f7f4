import { get } from "@/network"
import { INPRESS_API_URL } from "./constants"
import { LatLng } from "react-native-maps"

type RawGeoAreaStatus = {
  low_activity: boolean
}

export type GeoAreaStatus = {
  lowActivity: boolean
}

export const getGeoAreaStatus = async (
  point: LatLng,
): Promise<GeoAreaStatus> => {
  const token = process.env.EXPO_PUBLIC_PUBLIC_API_TOKEN

  type Params = {
    latitude: number
    longitude: number
  }

  const { low_activity } = await get<Params, RawGeoAreaStatus>(
    `${INPRESS_API_URL}/geo-area-status`,
    token,
    point,
  )

  return {
    lowActivity: low_activity,
  }
}
