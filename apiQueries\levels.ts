import { get } from "@/network"
import { INPRESS_API_URL } from "./constants"
import { cacheComponent } from "../utils/cacheComponent"
import {
  convertRawLevel,
  convertRawLevelStats,
  Level,
  LevelStats,
  RawLevel,
  RawLevelStats,
  RawStreak,
  Streak,
} from "@/types/levels"
import { ActiveConnectionMode } from "@/context/ModeContext"
import {
  convertRaw<PERSON>eaderboardUser,
  LeaderboardUser,
  RawLeaderboardUser,
} from "@/types/user"
import { Image } from "expo-image"

export const cacheLevelAssets = async (level: Level): Promise<Level> => {
  const cachedIconUri = await cacheComponent(level.iconUrl)
  const cachedBadgeUri = await cacheComponent(level.badgeUrl)
  const cachedGrayscaleBadgeUri = await cacheComponent(level.grayscaleBadgeUrl)
  const cachedShareableUri = await cacheComponent(level.shareableUrl)

  return {
    ...level,
    badgeUrl: cachedBadgeUri,
    grayscaleBadgeUrl: cachedGrayscaleBadgeUri,
    iconUrl: cachedIconUri,
    shareableUrl: cachedShareableUri,
  }
}

export const getLevels = async (token: string): Promise<Level[]> => {
  try {
    const response = await get<null, RawLevel[]>(
      `${INPRESS_API_URL}/levels`,
      token,
    )

    const levels = response.map(convertRawLevel)
    return await Promise.all(levels.map(cacheLevelAssets))
  } catch (error) {
    console.error("Error fetching levels:", error)
    throw error
  }
}

const streakLabels = [
  {
    type: "2_day",
    title: "2 Day Streak",
    subtitle:
      "Congrats on staying informed for 2 days in a row, you student of the world you.",
  },
  {
    type: "3_day",
    title: "3 Day Streak",
    subtitle:
      "Congrats on staying informed for 3 days in a row, you student of the world you.",
  },
  {
    type: "7_day",
    title: "7 Day Streak",
    subtitle:
      "Congrats on staying informed for a whole week, you student of the world you.",
  },
  {
    type: "14_day",
    title: "14 Day Streak",
    subtitle:
      "Congrats on staying informed for 2 weeks, you student of the world you.",
  },
  {
    type: "21_day",
    title: "21 Day Streak",
    subtitle:
      "Congrats on staying informed for 3 weeks, you student of the world you.",
  },
  {
    type: "4_week",
    title: "4 Week Streak",
    subtitle:
      "Congrats on staying informed for a month, you student of the world you.",
  },
  {
    type: "90_day",
    title: "90 Day Streak",
    subtitle:
      "Congrats on staying informed for 3 months, you student of the world you.",
  },
  {
    type: "180_day",
    title: "180 Day Streak",
    subtitle:
      "Congrats on staying informed for 6 months, you student of the world you.",
  },
  {
    type: "365_day",
    title: "365 Day Streak",
    subtitle:
      "Congrats on staying informed for a whole year, you student of the world you.",
  },
]

export const cacheStreakAssets = async (streak: Streak): Promise<Streak> => {
  const cachedShareableUri = await cacheComponent(streak.shareableUrl)
  return { ...streak, shareableUrl: cachedShareableUri }
}

export const convertRawStreak = (rawStreak: RawStreak): Streak => {
  return {
    type: rawStreak.type,
    title: streakLabels.find((label) => label.type === rawStreak.type)!.title,
    subtitle: streakLabels.find((label) => label.type === rawStreak.type)!
      .subtitle,
    shareableUrl: rawStreak.shareable_url,
  }
}

export const getStreaks = async (token: string): Promise<Streak[]> => {
  try {
    const response = await get<null, RawStreak[]>(
      `${INPRESS_API_URL}/streaks`,
      token,
    )

    const streaks = response.map(convertRawStreak)
    return await Promise.all(streaks.map(cacheStreakAssets))
  } catch (error) {
    console.error("Error fetching streaks:", error)
    throw error
  }
}

export const getStats = async (token: string): Promise<LevelStats> => {
  const response = await get<null, RawLevelStats>(
    `${INPRESS_API_URL}/levels/stats`,
    token,
  )

  return convertRawLevelStats(response)
}

export const getLeaderboard = async ({
  token,
  connectionMode,
}: {
  token: string
  connectionMode?: ActiveConnectionMode
}): Promise<LeaderboardUser[]> => {
  type Params = {
    connection_mode?: ActiveConnectionMode
  }

  const response = await get<
    Params,
    { leaderboard_users: RawLeaderboardUser[] }
  >(
    `${INPRESS_API_URL}/levels/leaderboard?use_new_api=true`,
    token,
    connectionMode ? { connection_mode: connectionMode } : undefined,
  )

  response.leaderboard_users.forEach((user) => {
    user.image && Image.prefetch(user.image?.url)
  })

  return response.leaderboard_users.map(convertRawLeaderboardUser)
}
