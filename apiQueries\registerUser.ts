import { GenderPrefs } from "@/components/signInOrUp/GenderPrefStep"
import { ConnectionMode } from "@/components/signInOrUp/ConnectionModeStep"
import {
  FinishedNewAccount,
  FinishedNewUser,
} from "@/components/signInOrUp/SignUp"
import { post } from "@/network"
import { INPRESS_API_URL } from "./constants"
import moment from "moment"
import { convertRawSession, RawSession, Session } from "@/types/user"

interface RegisterWithoutProfileParams {
  access_code?: string
  access_code_type?: string
  eula_agreement_date: string
  first_name: string
  last_name: string
  email: string
  phone_number: string
  password: string
  password_confirmation: string
  latitude: number
  longitude: number
  is_news_only: boolean
  push_token: string
}

const convertFinishedNewAccountToRaw = (
  account: FinishedNewAccount,
): RegisterWithoutProfileParams => ({
  access_code: account.accessCode,
  access_code_type: account.accessCodeType,
  eula_agreement_date: moment(account.eulaAgreementDate)
    .utc()
    .format("YYYY-MM-DD HH:mm:ss"),
  first_name: account.firstName,
  last_name: account.lastName,
  email: account.email,
  phone_number: account.phoneNumber,
  password: account.password,
  password_confirmation: account.passwordConfirmation,
  latitude: account.latitude,
  longitude: account.longitude,
  is_news_only: account.isNewsOnly,
  push_token: account.pushToken,
})

export const registerWithoutProfile = async (
  account: FinishedNewAccount,
): Promise<Session> => {
  const rawSession = await post<RegisterWithoutProfileParams, RawSession>(
    `${INPRESS_API_URL}/register-without-profile`,
    convertFinishedNewAccountToRaw(account),
  )
  return convertRawSession(rawSession)
}

export interface FinishedNewUserWithImageIds extends FinishedNewUser {
  imageIds: number[]
}

interface RegisterApiProps extends RegisterWithoutProfileParams {
  birthdate: string
  occupation: string
  gender: string
  connection_mode: ConnectionMode
  dates_mode_is_activated: boolean
  friends_mode_is_activated: boolean
  gender_prefs: GenderPrefs
  min_age_pref: number
  max_age_pref: number
  min_similarity_pref: number
  max_similarity_pref: number
  image_ids: number[]
  biography: string
  scoop_responses: {
    position: number
    prompt_id: number
    text: string
  }[]
}

const convertFinishedNewUserWithImageIdsToRaw = (
  account: FinishedNewUserWithImageIds,
): RegisterApiProps => ({
  ...convertFinishedNewAccountToRaw(account),
  birthdate: account.birthdate,
  occupation: account.occupation,
  gender: account.gender,
  connection_mode: account.connectionMode,
  dates_mode_is_activated: account.datesModeIsActivated,
  friends_mode_is_activated: account.friendsModeIsActivated,
  gender_prefs: account.genderPrefs,
  min_age_pref: account.minAgePref,
  max_age_pref: account.maxAgePref,
  min_similarity_pref: account.minSimilarityPref,
  max_similarity_pref: account.maxSimilarityPref,
  image_ids: account.imageIds,
  biography: account.biography,
  scoop_responses: account.scoopResponses.map(
    ({ position, promptId, text }) => ({
      position,
      prompt_id: promptId,
      text,
    }),
  ),
})

export const register = async (
  account: FinishedNewUserWithImageIds,
): Promise<Session> => {
  const rawSession = await post<RegisterApiProps, RawSession>(
    `${INPRESS_API_URL}/register`,
    convertFinishedNewUserWithImageIdsToRaw(account),
  )
  return convertRawSession(rawSession)
}
