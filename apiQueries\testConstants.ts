import moment from "moment"
import { FinishedNewUserWithImageIds } from "./registerUser"
import { birthdateFormat } from "@/types/user"

const NEW_PROFILE: FinishedNewUserWithImageIds = {
  accessCode: "TEST",
  accessCodeType: "custom",
  eulaAgreementDate: new Date(),
  firstName: "<PERSON>",
  lastName: "Doe",
  email: "<EMAIL>",
  phoneNumber: "1234567890",
  password: "password",
  passwordConfirmation: "password",
  birthdate: moment("2000-01-01").format(birthdateFormat),
  latitude: 38.9072,
  longitude: -77.0369,
  occupation: "Legal Assistant",
  gender: "female",
  connectionMode: "dates" as any,
  isNewsOnly: false,
  datesModeIsActivated: true,
  friendsModeIsActivated: false,
  genderPrefs: ["female", "male"],
  minAgePref: 25,
  maxAgePref: 30,
  minSimilarityPref: 0,
  maxSimilarityPref: 100,
  images: [],
  imageIds: [],
  biography: "Some text",
  scoopResponses: [
    { promptId: 3, position: 0, prompt: "Some text", text: "Some text" },
  ],
  pushToken: "abcd",
}

export { NEW_PROFILE }
