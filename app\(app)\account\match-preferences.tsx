import { getPreferencesOfBothModes } from "@/apiQueries/apiQueries"
import { updateProfile } from "@/apiQueries/auth"
import { Button } from "@/components/Button"
import { Screen } from "@/components/Themed"
import MatchPreferencesSection from "@/components/settings/MatchPreferencesSection"
import { ConnectionMode } from "@/components/signInOrUp/ConnectionModeStep"
import { ProfileMissing } from "@/components/widgets/ProfileMissing"
import { useSession } from "@/ctx"
import { isSessionWithProfile, Preferences, User } from "@/types/user"
import { router } from "expo-router"
import _ from "lodash"
import { useEffect, useState } from "react"
import { Al<PERSON>, ScrollView, StyleSheet, View } from "react-native"

interface SaveProps {
  modeStatuses: {
    datesModeIsActivated: boolean
    friendsModeIsActivated: boolean
  }
  datesPreferences?: Preferences
  friendsPreferences?: Preferences
}

export default function MatchPreferencesScreen() {
  const { session, refreshSession } = useSession()
  const [hasLoadedPreferences, setHasLoadedPreferences] = useState(false)
  const [datesPreferences, setDatesPreferences] = useState<Preferences>()
  const [friendsPreferences, setFriendsPreferences] = useState<Preferences>()

  useEffect(() => {
    if (session) {
      getPreferencesOfBothModes({
        token: session.token,
        userId: session.user.id,
      }).then(({ datesPreferences, friendsPreferences }) => {
        setDatesPreferences(datesPreferences)
        setFriendsPreferences(friendsPreferences)
        setHasLoadedPreferences(true)
      })
    }
  }, [session])

  if (!session || !hasLoadedPreferences) {
    return null
  }

  const handleSave = async ({
    modeStatuses: { datesModeIsActivated, friendsModeIsActivated },
    datesPreferences,
    friendsPreferences,
  }: SaveProps) => {
    await updateProfile({
      token: session.token,
      userUpdate: {
        datesModeIsActivated: datesModeIsActivated,
        friendsModeIsActivated: friendsModeIsActivated,
      },
      connectionMode: datesModeIsActivated
        ? ConnectionMode.Dates
        : ConnectionMode.Friends,
    })
    if (datesPreferences)
      await updateProfile({
        token: session.token,
        userUpdate: { preferences: datesPreferences },
        connectionMode: ConnectionMode.Dates,
      })

    if (friendsPreferences)
      await updateProfile({
        token: session.token,
        userUpdate: { preferences: friendsPreferences },
        connectionMode: ConnectionMode.Friends,
      })

    Alert.alert("Preferences updated!")
    refreshSession(session.token)
    router.back()
  }

  if (!isSessionWithProfile(session)) {
    return <ProfileMissing />
  }

  return (
    <MatchPreferencesScreen_
      user={session.user}
      initialDatesPreferences={datesPreferences}
      initialFriendsPreferences={friendsPreferences}
      onSave={handleSave}
    />
  )
}

interface MatchPreferencesScreenProps_ {
  user: User
  initialDatesPreferences: Preferences | undefined
  initialFriendsPreferences: Preferences | undefined
  onSave: (props: SaveProps) => void
}

export const MatchPreferencesScreen_ = ({
  user,
  initialDatesPreferences,
  initialFriendsPreferences,
  onSave,
}: MatchPreferencesScreenProps_) => {
  const [datesIsExpanded, setDatesExpanded] = useState(
    user.datesModeIsActivated,
  )
  const [friendsIsExpanded, setFriendsExpanded] = useState(
    user.friendsModeIsActivated,
  )
  const [datesPreferences, setDatesPreferences] = useState(
    initialDatesPreferences,
  )
  const [friendsPreferences, setFriendsPreferences] = useState(
    initialFriendsPreferences,
  )

  const datesIsStayingOn = datesIsExpanded && user.datesModeIsActivated
  const friendsIsStayingOn = friendsIsExpanded && user.friendsModeIsActivated
  const datesIsBeingRestored =
    datesIsExpanded && !user.datesModeIsActivated && !!datesPreferences
  const friendsIsBeingRestored =
    friendsIsExpanded && !user.friendsModeIsActivated && !!friendsPreferences

  return (
    <Screen style={styles.screen}>
      <ScrollView>
        <View style={styles.container}>
          <MatchPreferencesSection
            mode={ConnectionMode.Dates}
            modeIsActivated={user.datesModeIsActivated}
            isExpanded={datesIsExpanded}
            canBeCollapsed={friendsIsStayingOn || friendsIsBeingRestored}
            userCreatedAt={user.createdAt}
            onExpandedChange={setDatesExpanded}
            initialPreferences={datesPreferences}
            onPreferencesChange={setDatesPreferences}
          />

          <MatchPreferencesSection
            mode={ConnectionMode.Friends}
            modeIsActivated={user.friendsModeIsActivated}
            isExpanded={friendsIsExpanded}
            canBeCollapsed={datesIsStayingOn || datesIsBeingRestored}
            userCreatedAt={user.createdAt}
            onExpandedChange={setFriendsExpanded}
            initialPreferences={friendsPreferences}
            onPreferencesChange={setFriendsPreferences}
          />
        </View>
        <Button
          text="Save"
          onPress={() =>
            onSave({
              modeStatuses: {
                datesModeIsActivated: datesIsStayingOn || datesIsBeingRestored,
                friendsModeIsActivated:
                  friendsIsStayingOn || friendsIsBeingRestored,
              },
              datesPreferences,
              friendsPreferences,
            })
          }
        />
      </ScrollView>
    </Screen>
  )
}

const styles = StyleSheet.create({
  screen: { paddingHorizontal: 8 },
  container: {
    paddingVertical: 26,
    gap: 16,
  },
})
