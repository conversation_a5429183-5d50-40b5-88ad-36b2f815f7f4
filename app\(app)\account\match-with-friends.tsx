import { Screen } from "@/components/Themed"
import { KeyboardAvoidingView, Platform } from "react-native"
import ContactsList from "@/components/settings/matchWithFriends/ContactsList"
import { useSession } from "@/ctx"
import { useRouter } from "expo-router"
import { useEffect, useState } from "react"
import { Loader } from "@/components/widgets/Loader"
import { isUserWithProfile, UnknownTypeUserWithPrivateData } from "@/types/user"
import { getCurrentUser } from "@/apiQueries/auth"
import { MatchWithFriendsTeaser } from "@/screens/account/MatchWithFriendsTeaser"

export default function Route() {
  const { session } = useSession()
  const router = useRouter()
  const [user, setUser] = useState<UnknownTypeUserWithPrivateData | undefined>()

  useEffect(() => {
    getCurrentUser({ token: session!.token })
      .then(setUser)
      .catch((error) => {
        console.error("Error loading user data:", error)
      })
  }, [])

  useEffect(() => {
    if (user && !user?.phoneNumber) {
      router.replace("/(app)/account/phone-number")
    }
  }, [user])

  if (!user || !user.phoneNumber) {
    return <Loader />
  }

  if (!isUserWithProfile(user)) {
    return <MatchWithFriendsTeaser />
  }

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      style={{ flex: 1 }}
    >
      <Screen>
        <ContactsList />
      </Screen>
    </KeyboardAvoidingView>
  )
}
