import { StyleSheet, View } from "react-native"
import React, { useState } from "react"
import { useSession } from "@/ctx"
import { SegmentedButtons, SegmentedButtonsProps } from "react-native-paper"
import QrCodeScanner from "@/components/profile/QrCodeScanner"
import QrCodeView from "@/components/profile/QrCodeView"
import Colors from "@/constants/Colors"

const QrCodeScreen = () => {
  const { session } = useSession()
  const [screenShown, setScreenShown] = useState<"qr" | "scan">("qr")
  if (!session) return null

  const buttons: SegmentedButtonsProps["buttons"] = [
    { value: "scan", label: "Scan" },
    { value: "qr", label: "My Code" },
  ].map((b) => ({
    ...b,
    style:
      screenShown === b.value ? styles.selectedButton : styles.unselectedButton,
    labelStyle:
      screenShown === b.value ? styles.selectedLabel : styles.unselectedLabel,
  }))

  return (
    <View style={styles.mainContainer}>
      <SegmentedButtons
        value={screenShown}
        onValueChange={(value) => setScreenShown(value as "qr" | "scan")}
        buttons={buttons}
        style={styles.switchContainer}
      />
      {screenShown === "qr" ? <QrCodeView /> : <QrCodeScanner />}
    </View>
  )
}

export default QrCodeScreen

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    backgroundColor: Colors.light.background,
    justifyContent: "center",
    alignItems: "center",
    padding: 16,
  },
  switchContainer: {
    position: "absolute",
    top: 24,
    width: "100%",
    zIndex: 1000,
    padding: 4,
    backgroundColor: "white",
    borderRadius: 10,
  },
  selectedButton: {
    backgroundColor: "black",
    borderRadius: 10,
    borderTopRightRadius: 10,
    borderBottomRightRadius: 10,
    borderTopLeftRadius: 10,
    borderBottomLeftRadius: 10,
    borderColor: "transparent",
  },
  unselectedButton: {
    backgroundColor: "white",
    borderRadius: 10,
    borderColor: "transparent",
  },
  selectedLabel: {
    fontSize: 16,
    fontWeight: "600",
    color: "white",
  },
  unselectedLabel: {
    fontSize: 16,
    fontWeight: "600",
    color: "black",
  },
})
