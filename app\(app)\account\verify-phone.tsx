import { updateProfile, verifyPhoneNumber } from "@/apiQueries/auth"
import { Button } from "@/components/Button"
import VerifyPhoneStep from "@/components/signInOrUp/VerifyPhoneStep"
import { Screen, Text } from "@/components/Themed"
import { ScreenHeader } from "@/components/widgets/ScreenHeader"
import { useSession } from "@/ctx"
import { matchWithFriendsPath } from "@/utils/deepLinks"
import { useLocalSearchParams, useRouter } from "expo-router"
import { useState } from "react"
import { Platform, KeyboardAvoidingView, StyleSheet, View } from "react-native"

export default function Route() {
  const { session } = useSession()
  const router = useRouter()

  const [error, setError] = useState<string>()
  const [phoneVerificationCode, setPhoneVerificationCode] = useState("")

  const params = useLocalSearchParams()
  const phoneNumber = params.phoneNumber as string

  if (!phoneNumber) {
    router.replace("/(app)/account/phone-number")
  }

  const onPhoneVerificationSubmit = async () => {
    verifyPhoneNumber(phoneNumber, phoneVerificationCode)
      .then(async () => {
        await updateProfile({
          token: session!.token,
          userUpdate: { phoneNumber },
        })
        router.replace(matchWithFriendsPath)
      })
      .catch(() => {
        setError("Invalid code")
      })
  }

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      style={{ flex: 1 }}
    >
      <Screen>
        <ScreenHeader
          title="Enter your verification code"
          subtitle={`Sent to ${phoneNumber}`}
        />
        <View style={styles.container}>
          <VerifyPhoneStep onCodeChange={setPhoneVerificationCode} />
        </View>
        <Button text="Verify" onPress={onPhoneVerificationSubmit} />
        {error && <Text style={styles.errorMessage}>{error}</Text>}
      </Screen>
    </KeyboardAvoidingView>
  )
}

const styles = StyleSheet.create({
  container: {
    marginTop: 28,
    marginBottom: 16,
  },
  errorMessage: { color: "red", marginTop: 12, textAlign: "center" },
})
