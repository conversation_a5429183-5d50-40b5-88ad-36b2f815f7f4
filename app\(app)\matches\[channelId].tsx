import { useChatContext } from "@/chatContext"
import { Button } from "@/components/Button"
import { ChatScreen } from "@/components/ChatScreen"
import { Screen } from "@/components/Themed"
import { Loader } from "@/components/widgets/Loader"
import { ScreenHeader } from "@/components/widgets/ScreenHeader"
import { useActiveConnectionMode } from "@/context/ModeContext"
import { router, useLocalSearchParams } from "expo-router"
import { useEffect, useState } from "react"
import { View } from "react-native"

export default function ChatRoute() {
  const { channelId } = useLocalSearchParams() as { channelId: string }
  const [isLoading, setIsLoading] = useState(true)

  const { client, channel, setChannel } = useChatContext()

  useEffect(() => {
    if (!client) return
    setIsLoading(true)
    setChannel(null)

    client
      .queryChannels({ cid: { $in: [`messaging:${channelId}`] } })
      .then((channels) => {
        if (channels.length === 0) {
          console.error("Chat channel not found", channelId)
        } else {
          setChannel(channels[0])
        }
      })
      .catch((e) => {
        console.error("Error fetching chat channel", e)
      })
      .finally(() => {
        setIsLoading(false)
      })
  }, [client, channelId])

  const { activeConnectionMode } = useActiveConnectionMode()
  if (isLoading) return <Loader connectionMode={activeConnectionMode} />

  if (!channel) {
    console.error("Chat channel not found", channelId)
    return (
      <Screen>
        <View style={{ marginBottom: 40 }}>
          <ScreenHeader title="Oops, this chat could not be opened" />
        </View>
        <Button text="Go back" onPress={() => router.back()} />
      </Screen>
    )
  }
  return <ChatScreen channel={channel} />
}
