import { Chat } from "stream-chat-expo"
import { Stack, useFocusEffect } from "expo-router"
import { Loader } from "@/components/widgets/Loader"
import { useChatContext } from "@/chatContext"
import { useActiveConnectionMode } from "@/context/ModeContext"
import { useSession } from "@/ctx"
import { isUserWithProfile } from "@/types/user"
import { MatchesTeaser } from "@/screens/matches/MatchesTeaser"

export default function MatchesLayout() {
  const { client, clientIsConnected, triggerInitializeChat } = useChatContext()
  const { activeConnectionMode } = useActiveConnectionMode()
  const { session } = useSession()

  useFocusEffect(() => {
    if (
      isUserWithProfile(session!.user) &&
      (!client || !client.userID || !clientIsConnected)
    ) {
      console.log("No client, initializing chat")
      triggerInitializeChat()
    }
  })

  if (!isUserWithProfile(session!.user)) return <MatchesTeaser />

  if (!client || !clientIsConnected)
    return (
      <Loader
        connectionMode={activeConnectionMode}
        errorTag="chat client initialization"
      />
    )

  return (
    <Chat client={client}>
      <Stack>
        <Stack.Screen
          options={{
            headerShown: false,
            headerBackVisible: false,
          }}
          name="index"
        />
        <Stack.Screen
          options={{
            headerShown: false,
            headerBackVisible: false,
          }}
          name="chat"
        />
        <Stack.Screen
          options={{
            headerShown: false,
            headerBackVisible: false,
          }}
          name="[channelId]"
        />
      </Stack>
    </Chat>
  )
}
