import ActivateConnectionMode from "@/components/signInOrUp/ActivateConnectionMode"
import { ActiveConnectionMode } from "@/context/ModeContext"
import { useLocalSearchParams } from "expo-router"

export type ActivateParams = {
  presetConnectionMode?: ActiveConnectionMode
  isActivatingSocialStr?: "true" | "false"
}

export default function SignUpRoute() {
  const { presetConnectionMode, isActivatingSocialStr } =
    useLocalSearchParams<ActivateParams>()

  const isActivatingSocial = isActivatingSocialStr === "true"
  return (
    <ActivateConnectionMode
      presetConnectionMode={presetConnectionMode}
      isActivatingSocial={isActivatingSocial}
    />
  )
}
