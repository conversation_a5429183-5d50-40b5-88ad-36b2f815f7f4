import EditProfileScreen from "@/app/(app)/account/edit-profile"
import { SETTINGS, USER } from "./account"
import _ from "lodash"
import { PROFILE_SAVE_HANDLER } from "./edit-profile"

export default function Story() {
  return (
    <EditProfileScreen
      initialUser={{ ...USER, isNewsOnly: true }}
      userSettings={{ ...SETTINGS, autoUpdateLocation: false }}
      promptChoices={[]}
      onSave={PROFILE_SAVE_HANDLER}
    />
  )
}
