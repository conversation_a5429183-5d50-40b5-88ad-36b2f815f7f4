import EditProfileScreen from "@/app/(app)/account/edit-profile"
import { SETTINGS, USER } from "./account"
import { promptChoices } from "../(signInOrUp)/prompt-select"
import _ from "lodash"
import { PROFILE_SAVE_HANDLER } from "./edit-profile"

export default function Story() {
  return (
    <EditProfileScreen
      initialUser={USER}
      userSettings={{ ...SETTINGS, autoUpdateLocation: true }}
      promptChoices={_.range(20).map((i) => ({
        ...promptChoices[i % promptChoices.length],
        id: i,
      }))}
      onSave={PROFILE_SAVE_HANDLER}
    />
  )
}
