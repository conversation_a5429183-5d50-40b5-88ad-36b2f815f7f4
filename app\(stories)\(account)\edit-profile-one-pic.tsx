import EditProfileScreen from "@/app/(app)/account/edit-profile"
import { SETTINGS, USER } from "./account"
import { PROFILE_SAVE_HANDLER } from "./edit-profile"

export default function Story() {
  return (
    <EditProfileScreen
      initialUser={{ ...USER, images: USER.images.slice(0, 1) }}
      userSettings={{ ...SETTINGS, autoUpdateLocation: false }}
      promptChoices={[]}
      onSave={PROFILE_SAVE_HANDLER}
    />
  )
}
