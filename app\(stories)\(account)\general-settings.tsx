import { GeneralSettingsSection_ } from "@/components/settings/GeneralSettingsSection"
import { SESSION } from "../notifications/notification-feed"
import { Screen } from "@/components/Themed"
import { SETTINGS } from "./account"

export default function Story() {
  const handleSettingChange = async () => {
    return new Promise<void>((resolve) => {
      setTimeout(() => {
        console.log("Setting change simulated")
        resolve()
      }, 1000)
    })
  }

  return (
    <Screen>
      <GeneralSettingsSection_
        settings={SETTINGS}
        session={SESSION}
        onSettingChange={handleSettingChange}
        onSignOut={() => console.log("sign out")}
        onDeactivate={() => console.log("deactivate")}
      />
    </Screen>
  )
}
