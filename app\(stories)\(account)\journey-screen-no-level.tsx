import { JourneyScreen } from "@/screens/account/JourneyScreen"
import { USER } from "./account"
import { LEADERBOARD_USERS } from "../(news)/survey-completion/leaderboard"

export default function AccountStory() {
  return (
    <JourneyScreen
      user={{ ...USER, points: 10 }}
      streakDays={1}
      articlesRead={2}
      articlesRated={1}
      leaderboardUsers={LEADERBOARD_USERS}
      onUpdatePicture={async (uri) => console.log("uri", uri)}
    />
  )
}
