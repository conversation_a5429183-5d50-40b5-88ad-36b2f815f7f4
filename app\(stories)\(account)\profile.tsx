import { SETTINGS, USER } from "./account"
import { promptChoices } from "../(signInOrUp)/prompt-select"
import { ProfileScreen_ } from "@/screens/account/ProfileScreen"
import { ConnectionMode } from "@/components/signInOrUp/ConnectionModeStep"
import { ActiveConnectionMode } from "@/context/ModeContext"

export default function Story() {
  const props = {
    user: USER,
    userSettings: SETTINGS,
    connectionMode: ConnectionMode.Dates as ActiveConnectionMode,
    initialUser: USER,
    promptChoices,
    onSave: async () => {},
  }
  return <ProfileScreen_ {...props} />
}
