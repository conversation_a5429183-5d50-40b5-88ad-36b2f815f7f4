import { SetLocationSection } from "@/screens/account/SetLocationSection"
import { useState } from "react"
import { LatLng } from "react-native-maps"

export default function Story() {
  const [location, setLocation] = useState<LatLng>({
    latitude: 38.9072,
    longitude: -77.0369,
  })

  const handleSaveLocation = async (location: LatLng) => {
    await new Promise<void>((resolve) => {
      setTimeout(() => {
        console.log("Location saved", location)
        setLocation(location)
        resolve()
      }, 2000)
    })
  }

  return (
    <SetLocationSection
      defaultLocation={location}
      onSaveLocation={handleSaveLocation}
    />
  )
}
