import { ArticleGridLayout } from "@/components/news/ArticleGridLayout"
import { Screen } from "@/components/Themed"
import { articles } from "./feed/news"

export default function Story() {
  return (
    <Screen style={{ paddingHorizontal: 0 }}>
      <ArticleGridLayout
        articles={articles.map((a) => ({
          ...a,
          isOpened: true,
          isSurveyed: true,
        }))}
      />
    </Screen>
  )
}
