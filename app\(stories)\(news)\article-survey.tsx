import { ArticleSurvey } from "@/components/ratings/ArticleSurvey"
import { POINT_EVENTS } from "./survey-completion/level-3"
import _ from "lodash"

export default function ArticleSurveyStory() {
  return (
    <ArticleSurvey
      initialPoints={0}
      soundsAndHapticsOn
      onSurveyComplete={async () => ({
        pointEvents: POINT_EVENTS,
        leveledUp: false,
      })}
      onFetchLeaderboard={async () => []}
    />
  )
}
