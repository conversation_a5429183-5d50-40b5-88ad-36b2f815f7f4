import { NewsFeed } from "@/components/news/NewsFeed"
import { Screen } from "@/components/Themed"
import { NEWSFEED_PROPS } from "./news"
import { Announcement } from "@/apiQueries/newsFeed"

export const GENERIC_IMAGE_URL =
  "https://static.wixstatic.com/media/8c58d9_fd96730b9d654409b58de3c61521514d~mv2.png/v1/fill/w_748,h_548,al_c,q_90,usm_0.66_1.00_0.01,enc_avif,quality_auto/QR-existing%20friend%20(9).png"

export default function NewsFeedStory() {
  const announcements: Announcement[] = [
    {
      id: 1,
      title:
        "Will You Survive Squid Games Singles Hour in D.C. On The 28th? RSVP Here!",
      imageUrl:
        "https://i2-prod.themirror.com/incoming/article885594.ece/ALTERNATES/s615b/0_squid-game-1-7d9b00a291e34338b3e45a113d2b0014.jpg",
      url: "https://www.inpress.app",
    },
    {
      id: 2,
      title: "Invite Your Friends to InPress and Get a Free Week of Premium!",
      imageUrl: GENERIC_IMAGE_URL,
      url: "https://www.inpress.app",
    },
  ]

  return (
    <Screen style={{ paddingHorizontal: 0 }}>
      <NewsFeed {...NEWSFEED_PROPS} announcements={announcements} />
    </Screen>
  )
}
