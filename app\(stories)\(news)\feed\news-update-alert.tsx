import { NewsFeed } from "@/components/news/NewsFeed"
import { Screen } from "@/components/Themed"
import { NEWSFEED_PROPS } from "./news"
import { newUpdateAlert } from "@/components/news/alerts"
import { NewsFeedAlert } from "@/components/news/AlertCard"

export const alerts: NewsFeedAlert[] = [newUpdateAlert]

export default function NewsFeedStory() {
  return (
    <Screen style={{ paddingHorizontal: 0 }}>
      <NewsFeed {...NEWSFEED_PROPS} alerts={alerts} />
    </Screen>
  )
}
