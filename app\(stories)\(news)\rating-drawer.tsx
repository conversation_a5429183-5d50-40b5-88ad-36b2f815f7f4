import { Step } from "@/components/ratings/ArticleSurvey"
import { RatingDrawer } from "@/components/ratings/RatingDrawer"
import _ from "lodash"
import { Text } from "react-native"

export default function Story() {
  const step: Step = {
    title: "Title",
    subtitle: "Subtitle",
    nextIsDisabled: false,
    children: <Text>Children</Text>,
  }
  return (
    <RatingDrawer
      step={step}
      stepIndex={1}
      totalSteps={3}
      onBack={_.noop}
      onNext={_.noop}
      onClose={() => {}}
    />
  )
}
