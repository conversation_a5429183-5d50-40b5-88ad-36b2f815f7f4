import { Screen } from "@/components/Themed"
import _ from "lodash"
import { Leaderboard } from "@/components/ratings/Leaderboard"
import { LEADERBOARD_USERS } from "./leaderboard"

export default function Story() {
  const offset = _.random(1000, 5000)
  const users = LEADERBOARD_USERS.map((user, index) => ({
    ...user,
    points: user.points * 100 + offset,
    rank: index + offset,
  }))

  return (
    <Screen>
      <Leaderboard users={users} currentUserId={users[1].id} />
    </Screen>
  )
}
