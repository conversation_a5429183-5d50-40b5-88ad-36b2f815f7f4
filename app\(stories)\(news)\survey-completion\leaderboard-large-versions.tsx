import { Screen } from "@/components/Themed"
import _ from "lodash"
import { Leaderboard } from "@/components/ratings/Leaderboard"
import { LEADERBOARD_USERS } from "./leaderboard"
import { ScrollView, View } from "react-native"

export default function Story() {
  return (
    <Screen>
      <ScrollView>
        <View style={{ gap: 16 }}>
          <Leaderboard
            users={LEADERBOARD_USERS}
            currentUserId={LEADERBOARD_USERS[7].id}
            showLargeVersion={true}
          />
          <Leaderboard
            users={LEADERBOARD_USERS}
            currentUserId={LEADERBOARD_USERS[1].id}
            showLargeVersion={true}
          />
          <Leaderboard
            users={LEADERBOARD_USERS}
            currentUserId={LEADERBOARD_USERS[9].id}
            showLargeVersion={true}
          />
        </View>
      </ScrollView>
    </Screen>
  )
}
