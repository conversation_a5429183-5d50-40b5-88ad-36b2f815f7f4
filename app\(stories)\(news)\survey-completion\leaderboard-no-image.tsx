import { Screen } from "@/components/Themed"
import { Leaderboard } from "@/components/ratings/Leaderboard"
import { LEADERBOARD_USERS } from "./leaderboard"

export default function Story() {
  const usersWithoutImages = LEADERBOARD_USERS.map((user) => ({
    ...user,
    image: undefined,
  }))

  return (
    <Screen>
      <Leaderboard
        users={usersWithoutImages}
        currentUserId={usersWithoutImages[1].id}
      />
    </Screen>
  )
}
