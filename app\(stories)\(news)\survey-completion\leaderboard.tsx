import { USER } from "../../(account)/account"
import { Screen } from "@/components/Themed"
import _ from "lodash"
import { faker } from "@faker-js/faker"
import { Leaderboard } from "@/components/ratings/Leaderboard"
import { LeaderboardUser } from "@/types/user"

export const LEADERBOARD_USERS: LeaderboardUser[] = _(_.range(10))
  .map((i) => ({
    id: i,
    firstName: faker.person.firstName(),
    image: {
      ...USER.images[0],
      url: faker.image.urlPicsumPhotos(),
    },
    rank: 10 - i,
    points: USER.points + i,
    isArchived: false,
  }))
  .sortBy("points")
  .reverse()
  .value()

export default function Story() {
  return (
    <Screen>
      <Leaderboard
        users={LEADERBOARD_USERS}
        currentUserId={LEADERBOARD_USERS[6].id}
      />
    </Screen>
  )
}
