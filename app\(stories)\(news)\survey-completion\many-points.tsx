import { Step } from "@/components/ratings/ArticleSurvey"
import { RatingDrawer } from "@/components/ratings/RatingDrawer"
import _ from "lodash"
import { POINT_EVENTS } from "./level-3"
import { SoundLoadedPointsCompletionStep } from "@/components/ratings/SoundLoadedPointsCompletionStep"

export default function Story() {
  const step: Step = {
    children: (
      <SoundLoadedPointsCompletionStep
        initialPoints={5000}
        pointEvents={POINT_EVENTS}
        leaderboardUsers={undefined}
      />
    ),
  }
  return (
    <RatingDrawer step={step} stepIndex={3} totalSteps={4} onClose={_.noop} />
  )
}
