import { Step } from "@/components/ratings/ArticleSurvey"
import { RatingDrawer } from "@/components/ratings/RatingDrawer"
import { SoundLoadedPointsCompletionStep } from "@/components/ratings/SoundLoadedPointsCompletionStep"
import _ from "lodash"

export default function Story() {
  const step: Step = {
    children: (
      <SoundLoadedPointsCompletionStep
        initialPoints={40}
        pointEvents={[]}
        leaderboardUsers={undefined}
      />
    ),
  }
  return (
    <RatingDrawer step={step} stepIndex={3} totalSteps={4} onClose={_.noop} />
  )
}
