import { Step } from "@/components/ratings/ArticleSurvey"
import { RatingDrawer } from "@/components/ratings/RatingDrawer"
import _ from "lodash"
import { StreakStep } from "@/components/ratings/StreakStep"
import { STREAKS } from "../../_layout"

export default function Story() {
  const step: Step = { children: <StreakStep streakType={STREAKS[0].type} /> }
  return (
    <RatingDrawer step={step} stepIndex={3} totalSteps={4} onClose={_.noop} />
  )
}
