import { useState } from "react"
import { defaultNewUser, NewUser } from "@/components/signInOrUp/SignUp"
import { GenderPrefStep } from "@/components/signInOrUp/GenderPrefStep"

export default function Story() {
  const [profile, setProfile] = useState<NewUser>(defaultNewUser)

  return (
    <GenderPrefStep
      genderPrefs={profile.genderPrefs}
      onChange={(genderPrefs) => setProfile({ ...profile, genderPrefs })}
    />
  )
}
