import { useState } from "react"
import { PasswordStep } from "@/components/signInOrUp/PasswordStep"

export default function Story() {
  const [password, setPassword] = useState<string | undefined>(undefined)
  const [passwordConfirmation, setPasswordConfirmation] = useState<
    string | undefined
  >(undefined)

  return (
    <PasswordStep
      password={password}
      passwordConfirmation={passwordConfirmation}
      onChangePassword={(password) => setPassword(password)}
      onChangePasswordConfirmation={(passwordConfirmation) =>
        setPasswordConfirmation(passwordConfirmation)
      }
    />
  )
}
