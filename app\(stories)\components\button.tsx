import { Button } from "@/components/Button"
import { Spacer } from "@/components/Spacer"
import { Screen } from "@/components/Themed"

export default function ButtonStory() {
  return (
    <Screen>
      <Button
        text="Click me"
        iconSource={require("../../assets/BackgroundIntro.png")}
        onPress={() => console.log("Button clicked")}
        style={{ marginBottom: 20 }}
      />
      <Button
        text="Click me"
        iconSource={require("../../assets/BackgroundIntro.png")}
        isTextOnLeft={true}
        onPress={() => console.log("Button clicked")}
      />
    </Screen>
  )
}
