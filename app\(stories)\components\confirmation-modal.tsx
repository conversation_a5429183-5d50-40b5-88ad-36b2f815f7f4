import { ConfirmationModal } from "@/components/ConfirmationModal"
import { Screen } from "@/components/Themed"
import { useState } from "react"

export default function Story() {
  const [visible, setVisible] = useState(true)
  return (
    <Screen>
      <ConfirmationModal
        title="Report and block user"
        description="Are you sure you want to report and block this user? You will no longer receive messages from them. This cannot be undone."
        visible={visible}
        confirmButtonText="Report and block"
        onCancel={() => setVisible(false)}
        onConfirm={() => setVisible(false)}
      />
    </Screen>
  )
}
