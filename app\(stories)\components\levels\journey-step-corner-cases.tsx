import { Screen } from "@/components/Themed"
import { ScrollView, Text } from "react-native"
import JourneyStep from "@/components/levels/JourneyStep"
import { LEVELS } from "../badge"
import _ from "lodash"

export default function Story() {
  const lastLevelIndex = LEVELS.length - 1
  const lastLevel = LEVELS[lastLevelIndex]
  return (
    <ScrollView>
      <Screen>
        <Text>Many points</Text>
        <JourneyStep
          level={lastLevel}
          previousLevel={LEVELS[lastLevelIndex - 1]}
          points={lastLevel.pointsRequired - 10}
        />
      </Screen>
    </ScrollView>
  )
}
