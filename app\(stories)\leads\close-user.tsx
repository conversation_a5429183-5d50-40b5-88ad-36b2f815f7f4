import Swiper from "react-native-deck-swiper"
import { USER } from "../(account)/account"
import { LEADS } from "./leads"
import PersonCard from "@/components/leads/PersonCard"

export default function LeadCardStory() {
  return (
    <Swiper
      cards={[
        {
          ...LEADS[0],
          user: {
            ...LEADS[0].user,
            latitude: USER.latitude + 0.000001,
            longitude: USER.longitude,
          },
        },
      ]}
      verticalSwipe={false}
      renderCard={(lead) => (
        <PersonCard
          lead={lead}
          recipientUser={USER}
          onHideAndReport={() => {}}
        />
      )}
    />
  )
}
