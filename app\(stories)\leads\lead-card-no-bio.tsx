import Swiper from "react-native-deck-swiper"
import Person<PERSON><PERSON> from "@/components/leads/PersonCard"
import { LEADS } from "./leads"

export default function LeadCardStory() {
  return (
    <Swiper
      cards={[{ ...LEADS[0], user: { ...LEADS[0].user, biography: "" } }]}
      verticalSwipe={false}
      renderCard={(lead) => (
        <PersonCard
          lead={lead}
          recipientUser={LEADS[0].user}
          onHideAndReport={() => {}}
        />
      )}
    />
  )
}
