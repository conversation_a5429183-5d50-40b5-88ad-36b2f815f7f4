import Swiper from "react-native-deck-swiper"
import { USER } from "../(account)/account"
import { LEADS } from "./leads"
import PersonCard from "@/components/leads/PersonCard"

export default function LeadCardStory() {
  return (
    <Swiper
      cards={[LEADS[0]]}
      verticalSwipe={false}
      renderCard={(lead) => (
        <PersonCard
          lead={lead}
          recipientUser={USER}
          onHideAndReport={() => {}}
        />
      )}
    />
  )
}
