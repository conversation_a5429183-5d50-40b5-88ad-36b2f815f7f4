import { NotificationFeed_ } from "@/components/notifications/NotificationFeed"
import {
  AnnouncementNotification,
  NotificationType,
} from "@/apiQueries/notificationTypes"
import { SESSION } from "./notification-feed"

export default function Story() {
  const notifications: AnnouncementNotification[] = [
    {
      id: "1",
      type: NotificationType.Announcement,
      title: "InPress Squid Game Tomorrow!",
      body: "Join us at Metro Bar in DC from 7-9pm for a night of fun and games!",
      url: "https://www.inpress.app",
      isRead: false,
      createdAt: "2021-01-01T00:00:00Z",
    },
    {
      id: "2",
      type: NotificationType.Announcement,
      title: "Some other announcement!",
      body: "No link provided for this one, but have a great day!",
      isRead: false,
      createdAt: "2021-01-01T00:00:00Z",
    },
  ]

  return (
    <NotificationFeed_
      notifications={notifications}
      session={SESSION}
      onRefresh={() => {}}
      onNavigate={() => {}}
    />
  )
}
