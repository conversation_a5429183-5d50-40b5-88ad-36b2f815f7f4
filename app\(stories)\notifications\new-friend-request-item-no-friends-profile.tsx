import { Session } from "@/types/user"
import { USER } from "../(account)/account"
import { FRIEND_REQUEST_NOTIFICATION } from "./new-friend-request-item"
import { NotificationFeed_ } from "@/components/notifications/NotificationFeed"

const session: Session = {
  version: "1",
  user: { ...USER, friendsModeIsActivated: false },
  token: "123",
}

export default function Story() {
  return (
    <NotificationFeed_
      notifications={[FRIEND_REQUEST_NOTIFICATION]}
      session={session}
      onRefresh={() => {}}
      onNavigate={() => {}}
    />
  )
}
