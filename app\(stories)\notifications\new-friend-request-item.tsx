import {
  NewFriendRequestNotification,
  NotificationType,
} from "@/apiQueries/notificationTypes"
import { ConnectionMode } from "@/components/signInOrUp/ConnectionModeStep"
import { ConnectRequestStatus } from "@/apiQueries/connectRequests"
import { USER } from "../(account)/account"
import { NotificationFeed_ } from "@/components/notifications/NotificationFeed"
import { NOTIFICATIONS } from "./notification-feed"
import { Session } from "@/types/user"

export const FRIEND_REQUEST_NOTIFICATION: NewFriendRequestNotification = {
  ...(NOTIFICATIONS[0] as NewFriendRequestNotification),
  type: NotificationType.NewFriendRequest,
  connectRequest: {
    id: 1,
    connectionMode: ConnectionMode.Friends,
    status: ConnectRequestStatus.PENDING,
  },
}

const session: Session = {
  version: "1",
  user: { ...USER, friendsModeIsActivated: true },
  token: "123",
}

export default function NotificationFeedStory() {
  return (
    <NotificationFeed_
      notifications={[FRIEND_REQUEST_NOTIFICATION]}
      session={session}
      onRefresh={() => {}}
      onNavigate={() => {}}
    />
  )
}
