import {
  NewLikeNotification,
  NotificationType,
} from "@/apiQueries/notificationTypes"
import { ConnectionMode } from "@/components/signInOrUp/ConnectionModeStep"
import { NotificationFeed_ } from "@/components/notifications/NotificationFeed"
import { NOTIFICATIONS, SESSION } from "./notification-feed"

const NEW_LIKE_NOTIFICATION: NewLikeNotification = {
  ...NOTIFICATIONS[0],
  type: NotificationType.NewLike,
  connectionMode: ConnectionMode.Friends,
  user: undefined,
}

export default function NotificationFeedStory() {
  return (
    <NotificationFeed_
      notifications={[NEW_LIKE_NOTIFICATION]}
      session={SESSION}
      onRefresh={() => {}}
      onNavigate={() => {}}
    />
  )
}
