import { getSession } from "@/apiQueries/auth"
import MultiImagePicker from "@/components/MultiImagePicker"
import { TextInput } from "@/components/TextInput"
import { Session } from "@/types/user"
import { useEffect, useState } from "react"
import { View, Text } from "react-native"

export default function Story() {
  const [token, setToken] = useState<string>("")
  const [session, setSession] = useState<Session | null>(null)

  useEffect(() => {
    ;(async () => {
      try {
        const session = await getSession(token)
        setSession(session)
      } catch (e) {
        console.error(e)
        setSession(null)
      }
    })()
  }, [token])

  return (
    <View>
      <TextInput label="token" value={token} onChangeText={setToken} />
      {session && (
        <View>
          <Text>{session.user.firstName}</Text>
          <Text>Number of pics: {session.user.images.length}</Text>
          <MultiImagePicker
            onImagesChange={(images) => console.log(images)}
            initialImages={session?.user.images}
          />
        </View>
      )}
    </View>
  )
}
