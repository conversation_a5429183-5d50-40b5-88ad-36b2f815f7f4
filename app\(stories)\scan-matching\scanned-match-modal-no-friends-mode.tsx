import { ScannedMatchModal_ } from "@/components/leads/ScannedMatchModal"
import { defaultProps, scannedMatch } from "./scanned-match-modal"
import { USER } from "../(account)/account"
import { User } from "@/types/user"

const userWithoutFriendsMode: User = {
  ...USER,
  friendsModeIsActivated: false,
}

export default function Story() {
  return (
    <ScannedMatchModal_
      {...defaultProps}
      scannedMatch={{ ...scannedMatch, thisUser: userWithoutFriendsMode }}
    />
  )
}

export { scannedMatch }
