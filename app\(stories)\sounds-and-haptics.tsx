import { View, Text } from "react-native"
import { <PERSON><PERSON> } from "react-native-paper"
import { Audio } from "expo-av"

export default function Story() {
  const playSound = async () => {
    console.log("Playing sound")
    const soundObject = new Audio.Sound()
    await soundObject.loadAsync(require("@/assets/audio/level-up.mp3"))
    await soundObject.playAsync()
  }

  return (
    <View>
      <Button onPress={playSound}>
        <Text>Play sound</Text>
      </Button>
    </View>
  )
}
