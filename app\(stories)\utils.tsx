import { Screen } from "@/components/Themed"
import { pickImage, resizeImage } from "@/utils/images"
import { useState } from "react"
import { Text, View } from "react-native"
import { List } from "react-native-paper"
import { Image } from "expo-image"
import { USER } from "./(account)/account"

export default function Story() {
  const [result, setResult] = useState<any>()
  const [resultImageUri, setResultImageUri] = useState<string | null>(null)

  const updateResult = (result: any) => {
    setResult(JSON.stringify(result))
  }

  const run = async (func: () => Promise<any>) => {
    setResult("Running...")
    try {
      setResultImageUri(null)
      const result = await func()
      updateResult(result)
    } catch (e) {
      updateResult(e)
    }
  }

  const resizeMissingImage = async () => {
    run(() => resizeImage("somemissingimageurl"))
  }

  const resizeValidImage = async () => {
    run(async () => {
      let uri = USER.images[0].url
      uri = await resizeImage(uri)
      setResultImageUri(uri)
      return uri
    })
  }

  const resizePickedImage = async () => {
    run(async () => {
      let uri = await pickImage()
      if (!uri) {
        return "No image selected"
      }
      uri = await resizeImage(uri)
      setResultImageUri(uri)
      return uri
    })
  }

  return (
    <Screen>
      <List.Accordion title="Image Resizer">
        <List.Item title="Resize missing image" onPress={resizeMissingImage} />
        <List.Item title="Resize valid image" onPress={resizeValidImage} />
        <List.Item title="Resize picked image" onPress={resizePickedImage} />
      </List.Accordion>
      <View style={{ padding: 10 }} />
      <Text>Result:</Text>
      <Text>{result}</Text>
      {resultImageUri && (
        <Image
          source={{ uri: resultImageUri }}
          style={{ width: 200, height: 200 }}
        />
      )}
    </Screen>
  )
}
