import { StyleSheet, TouchableOpacity, Text, View } from "react-native"
import BackArrowIcon from "./icons/BackArrowIcon"
import { router } from "expo-router"
import { Image } from "expo-image"
import TabBar from "./widgets/TabBar"
import { matchesPath } from "@/utils/deepLinks"
import { UserImage } from "@/types/user"

interface ChatHeaderProps {
  userPhoto: UserImage
  userFirstName: string
  selectedTab: "chat" | "profile"
  setSelectedTab: (tab: "chat" | "profile") => void
}

export default function ChatHeader({
  userFirstName,
  userPhoto,
  selectedTab,
  setSelectedTab,
}: ChatHeaderProps) {
  const tabs = [
    {
      title: "Chat",
      onPress: () => setSelectedTab("chat"),
      selected: selectedTab === "chat",
    },
    {
      title: "Profile",
      onPress: () => setSelectedTab("profile"),
      selected: selectedTab === "profile",
    },
  ]
  return (
    <View style={styles.container}>
      <View style={styles.navigation}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.navigate(matchesPath)}
        >
          <BackArrowIcon />
        </TouchableOpacity>
        <View style={styles.userContainer}>
          <Image
            source={{ uri: userPhoto.url }}
            style={{ width: 35, height: 35, borderRadius: 100 }}
          />
          <Text style={styles.navigationText}>{userFirstName}</Text>
        </View>
      </View>
      <TabBar tabs={tabs} />
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    shadowColor: "black",
    shadowOffset: { width: 0, height: 7 },
    elevation: 4,
    shadowOpacity: 0.1,
    shadowRadius: 4,
    backgroundColor: "white",
    width: "100%",
    paddingTop: 10,
  },
  navigation: {
    paddingHorizontal: 5,
    gap: 4,
    flexDirection: "row",
    justifyContent: "flex-start",
    alignItems: "center",
  },
  backButton: {
    padding: 15,
  },
  navigationText: {
    fontSize: 19,
    fontFamily: "InterTight-Regular",
  },
  userContainer: {
    flexDirection: "row",
    gap: 10,
    alignItems: "center",
    backgroundColor: "white",
  },
})
