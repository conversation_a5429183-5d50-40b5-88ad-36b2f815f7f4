import React from "react"
import { View, Text, StyleSheet } from "react-native"
import Checkbox from "expo-checkbox"
import { BROWNSTONE } from "@/constants/Colors"

interface CheckboxRowProps<T> {
  options: { value: T; label: string }[]
  initialValues?: T[]
  minChecked?: number
  maxChecked?: number
  onChange: (checkedValues: T[]) => void
}

export function CheckboxRow<T>({
  options,
  initialValues,
  minChecked,
  maxChecked,
  onChange,
}: CheckboxRowProps<T>) {
  const [checkedValues, setCheckedValues] = React.useState<T[]>(
    initialValues || [],
  )

  const handleCheckboxChange = (value: T, isChecked: boolean) => {
    const newCheckedValues = isChecked
      ? [...checkedValues, value]
      : checkedValues.filter((v) => v !== value)

    setCheckedValues(newCheckedValues)
    onChange(newCheckedValues)
  }

  const isCheckBoxDisabled = (option: { value: T; label: string }) => {
    if (minChecked && maxChecked) return false

    const minNumberIsChecked = minChecked && checkedValues.length === minChecked
    const maxNumberIsChecked = maxChecked && checkedValues.length >= maxChecked

    return Boolean(
      (minNumberIsChecked && checkedValues.includes(option.value)) ||
        (maxNumberIsChecked && !checkedValues.includes(option.value)),
    )
  }

  return (
    <View style={styles.container}>
      {options.map((option) => (
        <View key={option.label} style={styles.option}>
          <Checkbox
            style={{ marginBottom: 8 }}
            color={BROWNSTONE}
            value={checkedValues.includes(option.value)}
            onValueChange={(isChecked) =>
              handleCheckboxChange(option.value, isChecked)
            }
            disabled={isCheckBoxDisabled(option)}
          />
          <Text>{option.label}</Text>
        </View>
      ))}
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "flex-start",
  },
  option: {
    flexDirection: "column",
    alignItems: "center",
    marginTop: 20,
    marginRight: 10,
    width: 80,
  },
})
