import React from "react"
import { View, Text, TouchableOpacity, StyleSheet } from "react-native"
import { Modal } from "./Modal"

interface ConfirmationModalProps {
  title: string
  description: string
  visible: boolean
  confirmButtonText: string
  children?: React.ReactNode
  onCancel: () => void
  onConfirm: () => void
}

export const ConfirmationModal = ({
  title,
  description,
  visible,
  confirmButtonText,
  children,
  onCancel,
  onConfirm,
}: ConfirmationModalProps) => {
  return (
    <Modal visible={visible}>
      <View style={styles.iconContainer}>
        <Text style={styles.icon}>!</Text>
      </View>
      <Text style={styles.title}>{title}</Text>
      <Text style={styles.description}>{description}</Text>
      {children}
      <TouchableOpacity style={styles.confirmButton} onPress={onConfirm}>
        <Text style={styles.confirmButtonText}>{confirmButtonText}</Text>
      </TouchableOpacity>
      <TouchableOpacity style={styles.cancelButton} onPress={onCancel}>
        <Text style={styles.cancelButtonText}>Cancel</Text>
      </TouchableOpacity>
    </Modal>
  )
}

const styles = StyleSheet.create({
  iconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: "#ffebee",
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 10,
  },
  icon: {
    fontSize: 24,
    color: "#d32f2f",
  },
  title: {
    fontSize: 18,
    fontWeight: "bold",
    textAlign: "center",
    marginBottom: 10,
  },
  description: {
    fontSize: 14,
    textAlign: "center",
    marginBottom: 20,
    color: "#757575",
  },
  confirmButton: {
    backgroundColor: "#d32f2f",
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 5,
    marginBottom: 10,
    width: "100%",
    alignItems: "center",
  },
  confirmButtonText: {
    color: "white",
    fontWeight: "bold",
  },
  cancelButton: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 5,
    backgroundColor: "#f5f5f5",
    width: "100%",
    alignItems: "center",
  },
  cancelButtonText: {
    color: "#757575",
  },
})
