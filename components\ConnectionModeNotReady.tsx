import { ConnectionMode } from "./signInOrUp/ConnectionModeStep"
import { EmptyState } from "./widgets/EmptyState"
import FontAwesome5 from "@expo/vector-icons/FontAwesome5"
import { Foundation } from "@expo/vector-icons"
import _ from "lodash"
import { datesModePalette, friendsModePalette } from "@/constants/Colors"
import { activateModePath } from "@/utils/deepLinks"
import { pushWithParams } from "@/utils/localParams"
import { ActivateParams } from "@/app/(signInOrUp)/activate-mode"
import { ActiveConnectionMode } from "@/context/ModeContext"

const ConnectionModeNotReady = ({
  connectionMode,
}: {
  connectionMode: ActiveConnectionMode
}) => {
  const handleButtonPress = () => {
    pushWithParams<ActivateParams>({
      pathname: activateModePath,
      params: { presetConnectionMode: connectionMode },
    })
  }

  return (
    <ConnectionModeNotReady_
      connectionMode={connectionMode}
      onButtonPress={handleButtonPress}
    />
  )
}

interface ConnectionModeNotReadyProps_ {
  connectionMode: ConnectionMode
  onButtonPress: () => void
}

const ConnectionModeNotReady_ = ({
  connectionMode,
  onButtonPress,
}: ConnectionModeNotReadyProps_) => {
  const modeLabel = _.startCase(connectionMode)
  const matchNoun = connectionMode === ConnectionMode.Dates ? "date" : "BFF"
  return (
    <EmptyState
      title="Let's get reacquainted!"
      subtitle={`Set up your ${modeLabel} profile to access the ${modeLabel} version of InPress! Hurry, you don't want to keep your new ${matchNoun} waiting...`}
      buttonText={`Create ${modeLabel} Profile`}
      buttonIcon={
        connectionMode === ConnectionMode.Dates ? (
          <Foundation name="heart" size={24} color="white" />
        ) : (
          <FontAwesome5 name="user-friends" size={24} color="white" />
        )
      }
      backgroundColor={
        connectionMode === ConnectionMode.Dates
          ? datesModePalette.backgroundColor
          : friendsModePalette.backgroundColor
      }
      onButtonPress={onButtonPress}
    />
  )
}

export default ConnectionModeNotReady
