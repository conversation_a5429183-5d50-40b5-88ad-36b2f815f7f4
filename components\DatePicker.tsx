import { Platform } from "react-native"
import { But<PERSON> } from "./Button"
import { DateTimePickerAndroid } from "@react-native-community/datetimepicker"
import DateTimePicker from "@react-native-community/datetimepicker"

export interface DatePickerProps {
  value: Date
  onChange: (selectedDate: Date) => void
  maximumDate?: Date
}

export const DatePicker = ({
  value,
  onChange,
  maximumDate,
}: DatePickerProps) => {
  const handleShowAndroidPicker = () => {
    DateTimePickerAndroid.open({
      value,
      onChange: (e, selectedDate) => selectedDate && onChange(selectedDate),
      mode: "date",
      maximumDate,
    })
  }

  if (Platform.OS === "android") {
    return (
      <Button text="Select date" onPress={() => handleShowAndroidPicker()} />
    )
  } else if (Platform.OS === "ios") {
    return (
      <DateTimePicker
        value={value}
        mode="date"
        display="spinner"
        onChange={(e, selectedDate) => selectedDate && onChange(selectedDate)}
        textColor="black"
        maximumDate={maximumDate}
      />
    )
  }
}
