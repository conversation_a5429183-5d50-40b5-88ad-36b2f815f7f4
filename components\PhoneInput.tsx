import MaskInput from "react-native-mask-input"
import { Keyboard, View } from "react-native"
import { Text } from "./Themed"
import React from "react"
import { styles } from "./TextInput"

type PhoneInputProps = {
  label?: string
  value?: string
  onChange?: (phoneNumber: string) => void
}

export default function PhoneInput({
  label,
  value,
  onChange,
}: PhoneInputProps) {
  return (
    <>
      {label && (
        <Text
          style={{
            marginBottom: 6,
            fontSize: 16,
          }}
        >
          {label}
        </Text>
      )}
      <View style={styles.inputContainer}>
        <MaskInput
          value={value}
          onChangeText={(_masked, unmasked) => onChange?.(unmasked)}
          mask={[
            "(",
            /\d/,
            /\d/,
            /\d/,
            ")",
            " ",
            /\d/,
            /\d/,
            /\d/,
            "-",
            /\d/,
            /\d/,
            /\d/,
            /\d/,
          ]}
          keyboardType="numeric"
          style={styles.input}
          returnKeyType="done"
          onSubmitEditing={() => Keyboard.dismiss()}
        />
      </View>
    </>
  )
}
