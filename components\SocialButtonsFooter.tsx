import React from "react"
import { View, StyleSheet } from "react-native"
import InstagramIcon from "./signInOrUp/assets/InstagramIcon"
import LinkedInIcon from "./signInOrUp/assets/LinkedInIcon"
import TiktokIcon from "./signInOrUp/assets/TiktokIcon"
import { SocialButton } from "./SocialButton"

export const SocialButtonsFooter = () => {
  return (
    <View style={styles.socialFooter}>
      <SocialButton
        url="https://www.instagram.com/inpresssocial/"
        iconComponent={<InstagramIcon />}
      />
      <SocialButton
        url="https://www.tiktok.com/@inpresssocial"
        iconComponent={<TiktokIcon />}
      />
      <SocialButton
        url="https://www.linkedin.com/company/inpress-app"
        iconComponent={<LinkedInIcon />}
      />
    </View>
  )
}

const styles = StyleSheet.create({
  socialFooter: {
    flexDirection: "row",
    justifyContent: "center",
    gap: 40,
    marginTop: 20,
  },
})
