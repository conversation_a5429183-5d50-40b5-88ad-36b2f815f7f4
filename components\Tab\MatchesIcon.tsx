import * as React from "react"
import Svg, { SvgProps, Path } from "react-native-svg"
import { UnreadCounter } from "../widgets/UnreadCounter"
import _ from "lodash"

interface MatchesIconProps extends SvgProps {
  notificationCount?: number
}

const MatchesIcon = ({ notificationCount, ...props }: MatchesIconProps) => (
  <>
    {!_.isNil(notificationCount) ? (
      <UnreadCounter count={notificationCount} />
    ) : null}
    <Svg width={20} height={20} fill="none" {...props}>
      <Path
        fill="#000"
        d="M8 0h4a8 8 0 1 1 0 16v3.5c-5-2-12-5-12-11.5a8 8 0 0 1 8-8Zm2 14h2a6 6 0 0 0 0-12H8a6 6 0 0 0-6 6c0 3.61 2.462 5.966 8 8.48V14Z"
      />
    </Svg>
  </>
)
export default MatchesIcon

export const MatchesSelectedIcon = (props: SvgProps) => (
  <Svg width={20} height={20} fill="none" {...props}>
    <Path
      fill="#000"
      d="M8 0h4a8 8 0 1 1 0 16v3.5c-5-2-12-5-12-11.5a8 8 0 0 1 8-8Z"
    />
  </Svg>
)
