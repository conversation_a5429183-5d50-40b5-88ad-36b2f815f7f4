import * as React from "react"
import Svg, { Path, SvgProps } from "react-native-svg"
import { UnreadCounter } from "../widgets/UnreadCounter"

interface NotificationIconProps extends SvgProps {
  notificationCount: number
}

const NotificationsIcon = ({
  notificationCount,
  ...props
}: NotificationIconProps) => (
  <>
    <UnreadCounter count={notificationCount} />
    <Svg width={24} height={24} viewBox="0 0 24 24" fill="none" {...props}>
      <Path
        d="M10.9783 19.7907H12.0217C11.8565 19.9292 11.6669 20 11.5 20C11.3331 20 11.1435 19.9292 10.9783 19.7907ZM17.5 16.907H5.5V9.98124C5.5 5.99832 8.3055 3 11.5 3C14.6945 3 17.5 5.99832 17.5 9.98124V16.907Z"
        stroke="black"
        strokeWidth={2}
      />
    </Svg>
  </>
)
export default NotificationsIcon

export const NotificationsSelectedIcon = () => (
  <Svg width={24} height={24} viewBox="0 0 24 24" fill="none">
    <Path
      d="M10.9783 19.7907H12.0217C11.8565 19.9292 11.6669 20 11.5 20C11.3331 20 11.1435 19.9292 10.9783 19.7907ZM17.5 16.907H5.5V9.98124C5.5 5.99832 8.3055 3 11.5 3C14.6945 3 17.5 5.99832 17.5 9.98124V16.907Z"
      stroke="black"
      fill={"black"}
      strokeWidth={2}
    />
  </Svg>
)
