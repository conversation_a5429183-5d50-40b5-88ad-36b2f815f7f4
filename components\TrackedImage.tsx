import { Image, ImageProps } from "expo-image"
import * as Sen<PERSON> from "@sentry/react-native"

import { useState } from "react"

type TimedImageProps = Omit<ImageProps, "onLoadStart" | "onLoadEnd"> & {
  name: string
}

export const TimedImage = ({ name, ...props }: TimedImageProps) => {
  const [span, setSpan] = useState<Sentry.Span | null>(null)

  const handleLoadStart = () => {
    const newSpan = Sentry.startInactiveSpan({
      name,
      op: "image.transaction",
    })

    if (newSpan) {
      setSpan(newSpan)
    }
  }

  const handleLoadEnd = () => {
    if (span) {
      span.end()
    }
  }

  return (
    <Image {...props} onLoadStart={handleLoadStart} onLoadEnd={handleLoadEnd} />
  )
}
