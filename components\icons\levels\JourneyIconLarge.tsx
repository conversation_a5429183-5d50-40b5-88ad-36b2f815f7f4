import Svg, { Path, SvgProps } from "react-native-svg"
const JourneyIconLarge = (props: SvgProps) => (
  <Svg width={25} height={25} viewBox="0 0 25 25" fill="none">
    <Path
      d="M5 2.50495C4.33696 2.50495 3.70108 2.76835 3.23223 3.23719C2.76339 3.70603 2.5 4.34191 2.5 5.00495C2.5 5.66799 2.76339 6.30388 3.23223 6.77272C3.70108 7.24156 4.33696 7.50495 5 7.50495C5.66304 7.50495 6.29893 7.24156 6.76777 6.77272C7.23661 6.30388 7.5 5.66799 7.5 5.00495C7.5 4.34191 7.23661 3.70603 6.76777 3.23719C6.29893 2.76835 5.66304 2.50495 5 2.50495ZM2.4446e-06 5.00495C-0.00120119 3.78712 0.442107 2.61072 1.24672 1.69655C2.05133 0.782373 3.16193 0.193286 4.37006 0.0398536C5.57819 -0.113579 6.80078 0.179192 7.80836 0.863215C8.81595 1.54724 9.53925 2.57548 9.8425 3.75495H20.1038L20.205 3.77245C21.2568 3.95809 22.2502 4.38842 23.105 5.0287C24.11 5.8012 25 7.02245 25 8.75495C25 10.4875 24.1125 11.7087 23.1038 12.4812C22.2493 13.1213 21.2563 13.5516 20.205 13.7375L20.1038 13.755H5C4.70375 13.755 4.035 13.94 3.4375 14.4175C2.87875 14.865 2.5 15.47 2.5 16.255C2.5 17.04 2.87875 17.645 3.4375 18.0925C4.035 18.57 4.70375 18.755 5 18.755H18.75V15.63L24.5837 20.005L18.75 24.38V21.255H5C4.04625 21.255 2.84 20.815 1.875 20.0425C0.871252 19.2412 2.4446e-06 17.9712 2.4446e-06 16.255C2.4446e-06 14.5387 0.871252 13.27 1.875 12.4675C2.84 11.6937 4.04625 11.255 5 11.255H19.8875C20.355 11.1612 21.0338 10.9175 21.5837 10.4975C22.1388 10.0725 22.5 9.52245 22.5 8.75495C22.5 7.98745 22.1375 7.4387 21.5837 7.01245C21.0791 6.64144 20.5006 6.38311 19.8875 6.25495H9.8425C9.53534 7.43056 8.8108 8.45419 7.80416 9.1347C6.79753 9.81521 5.57762 10.1061 4.37222 9.953C3.16683 9.79991 2.05836 9.21334 1.25379 8.3028C0.449214 7.39227 0.00354744 6.22002 2.4446e-06 5.00495Z"
      fill={props.fill || "white"}
    />
  </Svg>
)

export default JourneyIconLarge
