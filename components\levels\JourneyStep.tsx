import { Text, View, StyleSheet } from "react-native"
import { Card, ProgressBar } from "react-native-paper"
import { widthPercentageToDP as wp } from "react-native-responsive-screen"
import LockIcon from "../icons/levels/LockIcon"
import { abbreviateNumber } from "@/utils/numberAbbreviation"
import { progressBarStyle } from "../widgets/styles"
import { Image } from "expo-image"
import { Level } from "@/types/levels"

interface JourneyStepProps {
  level: Level
  points: number
  previousLevel?: Level
}

const JourneyStep = ({ level, points, previousLevel }: JourneyStepProps) => {
  const startingPoints = previousLevel?.pointsRequired || 0
  const progress =
    (points - startingPoints) / (level.pointsRequired - startingPoints)

  let status

  if (points < startingPoints) {
    status = "notStarted"
  } else if (points >= startingPoints && points < level.pointsRequired) {
    status = "inProgress"
  } else {
    status = "completed"
  }

  return (
    <Card elevation={0}>
      <Card.Content
        style={[
          styles.container,
          { borderWidth: status === "inProgress" ? 1 : 0 },
        ]}
      >
        <View style={styles.innerContainer}>
          {status === "notStarted" || status === "inProgress" ? (
            <View style={styles.lock}>
              <LockIcon fill={"white"} />
            </View>
          ) : null}
          <Image
            source={{
              uri:
                status === "completed"
                  ? level.badgeUrl
                  : level.grayscaleBadgeUrl,
            }}
            style={styles.badge}
          />

          <View style={{ flexShrink: 1, gap: 6 }}>
            <View style={styles.topRow}>
              <Text style={styles.levelName}>{level.name}</Text>
              <Text>{level.pointsRequired.toLocaleString()} Points</Text>
            </View>

            <View style={{ width: "100%", flexShrink: 1 }}>
              <Text style={styles.description}>{level.description}</Text>
            </View>
          </View>
        </View>
        {status === "inProgress" && (
          <View
            style={{
              flexDirection: "row",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <Text style={styles.pointsText}>
              {abbreviateNumber(previousLevel?.pointsRequired || 0)}
            </Text>
            <ProgressBar
              progress={progress}
              fillStyle={{ backgroundColor: level.color, ...progressBarStyle }}
              style={styles.progressBar}
            />
            <Text style={styles.pointsText}>
              {abbreviateNumber(level.pointsRequired)}
            </Text>
          </View>
        )}
      </Card.Content>
    </Card>
  )
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: "white",
    borderRadius: 8,
    paddingVertical: 10,
    paddingHorizontal: 10,
    gap: 10,
  },
  innerContainer: {
    flexDirection: "row",
    gap: 9,
    flex: 1,
  },
  lock: {
    position: "absolute",
    top: 0,
    left: 0,
    borderRadius: 14,
    borderColor: "black",
    borderWidth: 7,
    zIndex: 1,
    backgroundColor: "black",
  },
  pointsText: {
    flexShrink: 0,
    width: 30,
    textAlign: "center",
  },
  badge: {
    width: 70,
    height: 82,
  },
  topRow: {
    alignItems: "center",
    flexDirection: "row",
    gap: 8,
  },
  description: {
    flexWrap: "wrap",
    flexShrink: 1,
  },
  levelName: {
    fontSize: 16,
    fontFamily: "InterTight-SemiBold",
  },
  progressBar: {
    flex: 1,
    maxHeight: 9,
    maxWidth: wp(58),
    minWidth: wp(58),
    borderRadius: 9,
    backgroundColor: "black",
    marginHorizontal: 4,
  },
})

export default JourneyStep
