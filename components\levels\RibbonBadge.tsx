import { BADGE_SIZE } from "@/constants/Numbers"
import { Level } from "@/types/levels"
import { Image } from "expo-image"
import { router } from "expo-router"
import { View, StyleSheet, Text, TouchableOpacity } from "react-native"

type RibbonBadgeProps = {
  level?: Level
  showLabel?: boolean
}

const RibbonBadge = ({ level, showLabel }: RibbonBadgeProps) => {
  const handlePress = () => {
    router.push(`/account/journey`)
  }

  return (
    <TouchableOpacity onPress={handlePress}>
      <View style={styles.badgeContainer}>
        {level && (
          <View
            style={[styles.iconWrapper, { marginRight: showLabel ? -24 : 0 }]}
          >
            <View style={styles.badgeIconContainer}>
              <Image
                source={{ uri: level.badgeUrl }}
                style={styles.badgeImage}
              />
            </View>
          </View>
        )}
        {showLabel ? (
          <View
            style={[
              styles.labelContainer,
              level && styles.labelContainerWithIcon,
            ]}
          >
            <Text style={[styles.labelText, level && styles.labelTextWithIcon]}>
              {level ? level.name : "InScore Progress"}
            </Text>
          </View>
        ) : null}
      </View>
    </TouchableOpacity>
  )
}

const styles = StyleSheet.create({
  badgeContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  iconWrapper: {
    zIndex: 1,
    alignSelf: "center",
  },
  badgeIconContainer: {
    position: "relative",
    alignItems: "center",
    justifyContent: "center",
    marginBottom: -6,
  },
  badgeImage: {
    width: BADGE_SIZE,
    height: BADGE_SIZE,
    resizeMode: "contain",
  },
  labelContainer: {
    backgroundColor: "#000",
    paddingVertical: 4,
    paddingLeft: 14,
    paddingRight: 14,
    borderRadius: 16,
  },
  labelContainerWithIcon: {
    paddingLeft: 16,
  },
  labelText: {
    color: "#fff",
    fontSize: 14,
    fontFamily: "InterTight-Medium",
  },
  labelTextWithIcon: {
    textAlign: "left",
    marginLeft: 10,
  },
})

export default RibbonBadge
