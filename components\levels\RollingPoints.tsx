import { widthPercentageToDP as wp } from "react-native-responsive-screen"
import { StyleSheet, View } from "react-native"
import { Text } from "../Themed"
import Animated, { FadeInUp } from "react-native-reanimated"
import { PointEvent } from "@/types/levels"

type RollingPointsProps = {
  pointEvents: PointEvent[] | undefined
  eventCount: number
}

export const RollingPoints = ({
  pointEvents,
  eventCount,
}: RollingPointsProps) => {
  return (
    <View style={styles.pointEvents}>
      {pointEvents && eventCount > 0 && (
        <Animated.View
          key={eventCount}
          entering={FadeInUp.springify().duration(500)}
        >
          <Text style={styles.pointEventLabel}>
            {pointEvents[eventCount - 1].label}: +
            {pointEvents[eventCount - 1].points}
          </Text>
        </Animated.View>
      )}
    </View>
  )
}

export const styles = StyleSheet.create({
  pointEvents: {
    alignItems: "center",
    flexDirection: "row",
    position: "absolute",
    bottom: wp(-6.5),
    alignSelf: "flex-start",
    width: wp(55),
    overflow: "hidden",
  },

  pointEventLabel: {
    fontSize: 12,
  },
})
