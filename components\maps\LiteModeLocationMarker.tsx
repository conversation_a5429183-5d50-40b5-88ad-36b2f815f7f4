import { BROWNSTONE } from "@/constants/Colors"
import { IMAGES } from "@/constants/Images"
import { Image } from "expo-image"
import { StyleSheet, View, Text, Platform } from "react-native"
import { LatLng, Marker } from "react-native-maps"

interface LiteModeLocationMarkerProps {
  location: LatLng
  address: string
}

export const LiteModeLocationMarker = ({
  location,
  address,
}: LiteModeLocationMarkerProps) => (
  <Marker
    coordinate={{
      latitude: location.latitude,
      longitude: location.longitude,
    }}
  >
    <View style={styles.litemodeMarkerContainer}>
      <View style={styles.litemodeLocationText}>
        <Text style={{ fontSize: 12, color: "white" }}>{address}</Text>
      </View>
      <Image style={styles.litemodeLocationIcon} source={IMAGES.CaretDown} />
    </View>
  </Marker>
)
const styles = StyleSheet.create({
  litemodeMarkerContainer: {
    height: 40,
    alignSelf: "center",
  },
  litemodeLocationText: {
    backgroundColor: BROWNSTONE,
    paddingHorizontal: 20,
    padding: 5,
    borderRadius: 30,
  },
  litemodeLocationIcon: {
    position: "absolute",
    top: 23 + (Platform.OS === "android" ? 2 : 0),
    alignSelf: "center",
    tintColor: BROWNSTONE,
    resizeMode: "contain",
    height: 15,
    width: 15,
  },
})
