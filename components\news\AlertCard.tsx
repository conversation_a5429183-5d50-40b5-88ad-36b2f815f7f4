import { StyleSheet, Text, TouchableOpacity } from "react-native"
import { Card } from "react-native-paper"
import { miniCardStyles } from "./constants"
import { MaterialIcons } from "@expo/vector-icons"
import { EventType, trackEvent } from "@/utils/tracking"

enum AlertType {
  NewUpdate = "newUpdate",
  LaunchParty = "launchParty",
}

type NewsFeedAlert = {
  intro: string
  body: string
  type: AlertType
  onPress: () => void
  isCloseable?: boolean
}

type AlertCardProps = {
  alert: NewsFeedAlert
  onClose: (alert: NewsFeedAlert) => void
}

const AlertCard = ({ alert: alert, onClose }: AlertCardProps) => {
  const { intro, body, type, onPress, isCloseable } = alert

  const handlePress = () => {
    trackEvent(EventType.AnnouncementClicked, { data: { type } })
    onPress()
  }

  const handleClose = () => {
    trackEvent(EventType.AnnouncementClosed, { data: { type } })
    onClose(alert)
  }

  return (
    <Card style={styles.container} onPress={handlePress}>
      <Card.Content>
        {isCloseable && (
          <TouchableOpacity onPress={handleClose} style={styles.closeIcon}>
            <MaterialIcons name="close" size={24} color="white" />
          </TouchableOpacity>
        )}
        <Text style={{ paddingRight: isCloseable ? 15 : 0 }}>
          <Text style={miniCardStyles.intro}>{intro}</Text>{" "}
          <Text style={miniCardStyles.bodyText}>{body}</Text>
        </Text>
      </Card.Content>
    </Card>
  )
}

const HEIGHT = 75

const styles = StyleSheet.create({
  container: {
    ...miniCardStyles.container,
    height: HEIGHT,
  },
  closeIcon: {
    position: "absolute",
    top: 10,
    right: 10,
    zIndex: 1,
  },
})

export default AlertCard
export { NewsFeedAlert, AlertType, HEIGHT }
