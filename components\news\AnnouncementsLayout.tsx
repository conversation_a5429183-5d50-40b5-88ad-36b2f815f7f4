import { ScrollView, StyleSheet, View } from "react-native"
import { router } from "expo-router"
import {
  navigateToInAppDestination,
  newsfeedMarginHorizontal,
  SPOTLIGHT_IMAGE_HEIGHT,
} from "./constants"
import { Announcement } from "@/apiQueries/newsFeed"
import { CompactSpotlightCard } from "./CompactSpotlightCard"
import { EventType, trackEvent } from "@/utils/tracking"

interface AnnouncementsLayoutProps {
  announcements: Announcement[]
}

export const AnnouncementsLayout = ({
  announcements,
}: AnnouncementsLayoutProps) => {
  const handlePress = async (announcement: Announcement) => {
    trackEvent(EventType.AnnouncementClicked, {
      data: { announcement_id: announcement.id },
    })
    if (announcement.url) router.navigate(announcement.url)
    else if (announcement.inAppDestination) {
      navigateToInAppDestination(announcement.inAppDestination)
    } else {
      console.error("Announcement has no URL or inAppDestination")
    }
  }

  return (
    <View style={[styles.container]}>
      <ScrollView horizontal showsHorizontalScrollIndicator={false}>
        {announcements.map((announcement) => (
          <View style={styles.announcement} key={announcement.id}>
            <CompactSpotlightCard
              announcement={announcement}
              onPress={handlePress}
            />
          </View>
        ))}
      </ScrollView>
    </View>
  )
}

export const HEIGHT = SPOTLIGHT_IMAGE_HEIGHT

const styles = StyleSheet.create({
  container: {
    marginLeft: newsfeedMarginHorizontal,
    height: HEIGHT,
  },
  announcement: {
    width: 276,
    marginRight: 20,
  },
})
