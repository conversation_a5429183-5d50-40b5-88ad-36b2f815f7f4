import { StyleSheet, TouchableOpacity } from "react-native"
import { Announcement } from "@/apiQueries/newsFeed"
import { Image } from "expo-image"
import { SPOTLIGHT_IMAGE_HEIGHT } from "./constants"
import { LinearGradient } from "expo-linear-gradient"
import { Text } from "../Themed"

interface SpotlightArticleCardProps {
  announcement: Announcement
  onPress: (announcement: Announcement) => void
}

export const CompactSpotlightCard = ({
  announcement,
  onPress,
}: SpotlightArticleCardProps) => {
  return (
    <TouchableOpacity
      style={styles.headlineContainer}
      onPress={() => onPress(announcement)}
    >
      <Image
        source={{ uri: announcement.imageUrl }}
        style={styles.headlineImage}
      />
      <LinearGradient
        colors={[
          "rgba(0, 0, 0, 0)",
          "rgba(45, 45, 45, 0.45)",
          "rgba(0, 0, 0, 1)",
        ]}
        locations={[0.2, 0.45, 1]}
        style={styles.linearGradient}
      />
      <Text style={styles.headlineText} numberOfLines={3}>
        {announcement.title}
      </Text>
    </TouchableOpacity>
  )
}

const styles = StyleSheet.create({
  headlineContainer: {
    height: SPOTLIGHT_IMAGE_HEIGHT,
    backgroundColor: "transparent",
    shadowColor: "transparent",
  },
  headlineImage: {
    borderRadius: 8,
    height: SPOTLIGHT_IMAGE_HEIGHT,
    width: "100%",
  },
  headlineText: {
    marginHorizontal: 12,
    marginBottom: 18,
    position: "absolute",
    bottom: 0,
    fontSize: 16,
    fontFamily: "InterTight-SemiBold",
    color: "white",
    alignItems: "center",
  },
  linearGradient: {
    position: "absolute",
    bottom: 0,
    width: "100%",
    height: "65%",
    borderRadius: 8,
  },
})
