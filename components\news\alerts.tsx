import { NewsFeedAlert, AlertType } from "./AlertCard"
import AsyncStorage from "@react-native-async-storage/async-storage"
import { openAppStore } from "@/utils/general"

const newUpdateAlert: NewsFeedAlert = {
  type: AlertType.NewUpdate,
  intro: "EXTRA! EXTRA!",
  body: "In<PERSON><PERSON> has a new update. Tap here or go to the app store to update now.",
  onPress: openAppStore,
}

const CLOSED_ALERTS_KEY = "closedAlertTypes"

const getClosedAlerts = async () => {
  const closedAlertTypesJson = await AsyncStorage.getItem(CLOSED_ALERTS_KEY)

  const closedAlertTypes: AlertType[] = closedAlertTypesJson
    ? JSON.parse(closedAlertTypesJson)
    : []

  return closedAlertTypes
}

const setAlertAsClosed = async (alert: NewsFeedAlert) => {
  const alertClosures = await getClosedAlerts()
  await AsyncStorage.setItem(
    CLOSED_ALERTS_KEY,
    JSON.stringify([...alertClosures, alert.type]),
  )
}

export { newUpdateAlert, CLOSED_ALERTS_KEY, getClosedAlerts, setAlertAsClosed }
