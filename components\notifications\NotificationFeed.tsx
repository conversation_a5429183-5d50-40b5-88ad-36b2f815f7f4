import {
  StyleSheet,
  Text,
  View,
  SectionList,
  SectionListData,
} from "react-native"
import { useCallback, useEffect, useState } from "react"
import { heightPercentageToDP as hp } from "react-native-responsive-screen"
import moment from "moment"
import { groupBy } from "lodash"
import { Notification, NotificationType } from "@/apiQueries/notificationTypes"
import { useSession } from "@/ctx"
import NotificationItem from "./NotificationItem"
import { Loader } from "../widgets/Loader"
import {
  ActiveConnectionMode,
  useActiveConnectionMode,
} from "@/context/ModeContext"
import { router } from "expo-router"
import {
  getNotifications,
  markNotificationsAsRead,
} from "@/apiQueries/notifications"
import { Session } from "@/types/user"
import { HrefObject } from "@/utils/localParams"

function formatDate(dateInput: string) {
  return moment(dateInput).calendar({
    sameDay: "[Today]",
    lastDay: "[Yesterday]",
    lastWeek: "[Last] dddd",
    sameElse: "MMMM Do",
  })
}

function mapSections(data: Notification[]): SectionListData<Notification>[] {
  const groupedData = groupBy(data, (notification) =>
    moment(notification.createdAt).startOf("day").toISOString(),
  )

  return Object.entries(groupedData)
    .sort(([dateA], [dateB]) => moment(dateB).diff(moment(dateA)))
    .map(([_, data]) => ({
      title: data[0].createdAt,
      data: data.sort((a, b) => moment(b.createdAt).diff(moment(a.createdAt))),
    }))
}

export interface HandleNavigateParams {
  href: HrefObject
  connectionMode: ActiveConnectionMode
}

export default function NotificationFeed() {
  const [notifications, setNotifications] = useState<Notification[]>()
  const [isLoading, setIsLoading] = useState(true)
  const { session } = useSession()
  const { setActiveConnectionMode } = useActiveConnectionMode()

  const fetchNotifications = async () => {
    if (!session) return
    const notifications = await getNotifications(session.token)

    const displayableNotifications = notifications.filter(
      (n) => n.type !== NotificationType.NewMessage,
    )

    const unreadNotifications = displayableNotifications.filter(
      (n) => !n.isRead,
    )

    if (unreadNotifications.length > 0) {
      await markNotificationsAsRead(
        unreadNotifications.map((n) => n.id),
        session.token,
      )
    }

    setNotifications(displayableNotifications)
    setIsLoading(false)
  }

  const onRefresh = () => {
    fetchNotifications()
  }

  const handleNavigate = ({ href, connectionMode }: HandleNavigateParams) => {
    setActiveConnectionMode(connectionMode)
    router.navigate(href)
  }

  useEffect(() => {
    fetchNotifications()
  }, [])

  if (isLoading || notifications == null) return <Loader />

  return (
    <NotificationFeed_
      notifications={notifications}
      session={session}
      onNavigate={handleNavigate}
      onRefresh={onRefresh}
    />
  )
}

export function NotificationFeed_({
  notifications,
  session,
  onNavigate,
  onRefresh,
}: {
  notifications: Notification[]
  session: Session | null | undefined
  onNavigate: (params: HandleNavigateParams) => void
  onRefresh: () => void
}) {
  const [isRefreshing, setIsRefreshing] = useState(false)

  const handleRefresh = useCallback(() => {
    setIsRefreshing(true)
    onRefresh()
    setIsRefreshing(false)
  }, [onRefresh, setIsRefreshing])

  const sections = notifications ? mapSections(notifications) : []

  return (
    <SectionList
      sections={sections}
      style={{ backgroundColor: "#FFF" }}
      scrollEnabled
      bounces
      renderSectionHeader={({ section }) => (
        <View
          style={{
            paddingHorizontal: "5%",
            paddingVertical: "3.5%",
            borderTopWidth: 1,
            borderColor: "#E3E3E3",
            backgroundColor: "#FFF",
          }}
        >
          <Text
            style={{
              fontSize: 16,
              fontFamily: "InterTight-SemiBold",
              letterSpacing: 0.39,
            }}
          >
            {formatDate(section.title)}
          </Text>
        </View>
      )}
      renderItem={({ item }) => (
        <NotificationItem
          item={item}
          session={session}
          onNavigate={onNavigate}
          onRefresh={onRefresh}
        />
      )}
      onRefresh={handleRefresh}
      refreshing={isRefreshing}
      onEndReached={() => {}}
      ListEmptyComponent={() => {
        return (
          <View style={styles.emptyListContainer}>
            <Text style={styles.noNotificationsLabel}>
              No notifications yet
            </Text>
            <Text style={styles.noNotificationDesc}>
              We’ll let you know when notifications arrive.
            </Text>
          </View>
        )
      }}
    />
  )
}

const styles = StyleSheet.create({
  emptyListContainer: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    minHeight: hp(70),
  },
  noNotificationsLabel: {
    color: "#000000",
    fontSize: 16,
    fontFamily: "InterTight-SemiBold",
    paddingBottom: 10,
  },
  noNotificationDesc: {
    color: "#000000",
    fontSize: 14,
    fontFamily: "InterTight-SemiBold",
    width: "50%",
    textAlign: "center",
    alignSelf: "center",
  },
})
