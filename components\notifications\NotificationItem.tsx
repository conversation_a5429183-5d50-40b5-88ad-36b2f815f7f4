import {
  NewFriendRequestNotificationItem,
  NewMatchNotificationItem,
  NewLikeNotificationItem,
  FriendRequestAcceptedNotificationItem,
} from "./items"
import { HandleNavigateParams } from "./NotificationFeed"
import { LevelUpNotificationItem } from "./items/LevelUpNotificationItem"
import { NotificationType, Notification } from "@/apiQueries/notificationTypes"
import { AlmostLevelUpNotificationItem } from "./items/AlmostLevelUpNotificationItem"
import { AlmostLosingStreakNotificationItem } from "./items/AlmostLosingStreakNotificationItem"
import { LevelsFeatureLiveNotificationItem } from "./items/LevelsFeatureLiveNotificationItem"
import { AlmostHitDensityBonus1NotificationItem } from "./items/AlmostHitDensityBonus1NotificationItem"
import { AlmostHitDensityBonus2NotificationItem } from "./items/AlmostHitDensityBonus2NotificationItem"
import { AlmostHitStreakNotificationItem } from "./items/AlmostHitStreakNotificationItem"
import { HitStreakNotificationItem } from "./items/HitStreakNotificationItem"
import { HitDensityBonus1NotificationItem } from "./items/HitDensityBonus1NotificationItem"
import { HitDensityBonus2NotificationItem } from "./items/HitDensityBonus2NotificationItem"
import { LevelsWeeklyRecapNotificationItem } from "./items/LevelsWeeklyRecapNotificationItem"
import { AnnouncementNotificationItem } from "./items/AnnouncementNotificationItem"
import { isSessionWithProfile, Session } from "@/types/user"

interface NotificationItemProps {
  item: Notification
  session: Session | null | undefined
  onNavigate: (params: HandleNavigateParams) => void
  onRefresh: () => void
}

export default function NotificationItem({
  item,
  ...props
}: NotificationItemProps) {
  switch (item.type) {
    case NotificationType.Announcement:
      return <AnnouncementNotificationItem item={item} {...props} />
    case NotificationType.NewFriendRequest:
      const { session } = props
      if (session && isSessionWithProfile(session)) {
        return (
          <NewFriendRequestNotificationItem
            item={item}
            {...props}
            session={session}
          />
        )
      }
      return null
    case NotificationType.NewMatch:
      return <NewMatchNotificationItem item={item} {...props} />
    case NotificationType.NewLike:
      return <NewLikeNotificationItem item={item} {...props} />
    case NotificationType.FriendRequestAccepted:
      return <FriendRequestAcceptedNotificationItem item={item} {...props} />
    case NotificationType.LevelUp:
      return <LevelUpNotificationItem item={item} {...props} />
    case NotificationType.AlmostLevelUp:
      return <AlmostLevelUpNotificationItem item={item} {...props} />
    case NotificationType.LevelsFeatureLive:
      return <LevelsFeatureLiveNotificationItem item={item} {...props} />
    case NotificationType.AlmostLosingStreak:
      return <AlmostLosingStreakNotificationItem item={item} {...props} />
    case NotificationType.AlmostHitStreak:
      return <AlmostHitStreakNotificationItem item={item} {...props} />
    case NotificationType.AlmostHitDensityBonus1:
      return <AlmostHitDensityBonus1NotificationItem item={item} {...props} />
    case NotificationType.AlmostHitDensityBonus2:
      return <AlmostHitDensityBonus2NotificationItem item={item} {...props} />
    case NotificationType.HitDensityBonus1:
      return <HitDensityBonus1NotificationItem item={item} {...props} />
    case NotificationType.HitDensityBonus2:
      return <HitDensityBonus2NotificationItem item={item} {...props} />
    case NotificationType.HitDayOrWeekStreak:
    case NotificationType.HitMonthStreak:
    case NotificationType.HitLongTermStreak:
      return <HitStreakNotificationItem item={item} {...props} />
    case NotificationType.LevelsWeeklyRecap:
      return <LevelsWeeklyRecapNotificationItem item={item} {...props} />
    default:
      return null
  }
}
