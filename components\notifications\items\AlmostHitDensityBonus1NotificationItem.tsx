import { GenericNotificationItem } from "./GenericNotificationItem"
import { AlmostHitDensityBonus1Notification } from "@/apiQueries/notificationTypes"
import { NormalText } from "@/components/StyledText"
import { newsfeedButtonProps } from "../buttonProps"
import { InPressLogo } from "../InPressLogo"

export function AlmostHitDensityBonus1NotificationItem({
  item,
}: {
  item: AlmostHitDensityBonus1Notification
}) {
  return (
    <GenericNotificationItem
      timestamp={item.createdAt}
      LogoComponent={<InPressLogo />}
      TextComponent={
        <NormalText>
          One more article today earns you 10 bonus points. Keep going!
        </NormalText>
      }
      primaryButton={newsfeedButtonProps}
    />
  )
}
