import { GenericNotificationItem } from "./GenericNotificationItem"
import { NormalText } from "@/components/StyledText"
import { newsfeedButtonProps } from "../buttonProps"
import { InPressLogo } from "../InPressLogo"
import { AlmostHitDensityBonus2Notification } from "@/apiQueries/notificationTypes"

export function AlmostHitDensityBonus2NotificationItem({
  item,
}: {
  item: AlmostHitDensityBonus2Notification
}) {
  return (
    <GenericNotificationItem
      timestamp={item.createdAt}
      LogoComponent={<InPressLogo />}
      TextComponent={
        <NormalText>
          Rate just 1 more article today to unlock a 25-point bonus!
        </NormalText>
      }
      primaryButton={newsfeedButtonProps}
    />
  )
}
