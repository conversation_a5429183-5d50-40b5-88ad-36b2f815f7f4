import { GenericNotificationItem } from "./GenericNotificationItem"
import { AlmostHitStreakNotification } from "@/apiQueries/notificationTypes"
import { BoldText, NormalText } from "@/components/StyledText"
import { newsfeedButtonProps } from "../buttonProps"
import { InPressLogo } from "../InPressLogo"
import { useLevels } from "@/context/LevelContext"

export function AlmostHitStreakNotificationItem({
  item,
}: {
  item: AlmostHitStreakNotification
}) {
  const { loading, streaks } = useLevels()

  if (loading || !streaks) {
    return null
  }

  const streak = streaks.find((s) => s.type === item.streakTypeToHit)

  return (
    <GenericNotificationItem
      timestamp={item.createdAt}
      LogoComponent={<InPressLogo />}
      TextComponent={
        <NormalText>
          Just <BoldText>1 more day</BoldText> to hit your {streak!.title} and
          earn {item.rewardPoints} points!
        </NormalText>
      }
      primaryButton={newsfeedButtonProps}
    />
  )
}
