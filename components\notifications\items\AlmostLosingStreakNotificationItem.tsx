import { GenericNotificationItem } from "./GenericNotificationItem"
import { AlmostLosingStreakNotification } from "@/apiQueries/notificationTypes"
import { BoldText, NormalText } from "@/components/StyledText"
import { newsfeedButtonProps } from "../buttonProps"
import { InPressLogo } from "../InPressLogo"

export function AlmostLosingStreakNotificationItem({
  item,
}: {
  item: AlmostLosingStreakNotification
}) {
  return (
    <GenericNotificationItem
      timestamp={item.createdAt}
      LogoComponent={<InPressLogo />}
      TextComponent={
        <NormalText>
          Your streak is about to reset! Rate an article in the{" "}
          <BoldText>next 3 hours</BoldText> to save it.
        </NormalText>
      }
      primaryButton={newsfeedButtonProps}
    />
  )
}
