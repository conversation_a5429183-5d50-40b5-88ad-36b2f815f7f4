import { GenericNotificationItem } from "./GenericNotificationItem"
import { AnnouncementNotification } from "@/apiQueries/notificationTypes"
import { BoldText, NormalText } from "@/components/StyledText"
import { InPressLogo } from "../InPressLogo"
import { router } from "expo-router"

export function AnnouncementNotificationItem({
  item,
}: {
  item: AnnouncementNotification
}) {
  const url = item.url

  return (
    <GenericNotificationItem
      timestamp={item.createdAt}
      LogoComponent={<InPressLogo />}
      TextComponent={
        <NormalText>
          <BoldText>{item.title}</BoldText>
          {` `}
          {item.body}
        </NormalText>
      }
      primaryButton={
        url
          ? {
              text: "Open link",
              onPress: () => router.navigate(url),
            }
          : undefined
      }
    />
  )
}
