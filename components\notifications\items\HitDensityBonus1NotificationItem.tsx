import { GenericNotificationItem } from "./GenericNotificationItem"
import { HitDensityBonus1Notification } from "@/apiQueries/notificationTypes"
import { NormalText } from "@/components/StyledText"
import { journeyButtonProps } from "../buttonProps"
import { InPressLogo } from "../InPressLogo"

export function HitDensityBonus1NotificationItem({
  item,
}: {
  item: HitDensityBonus1Notification
}) {
  return (
    <GenericNotificationItem
      timestamp={item.createdAt}
      LogoComponent={<InPressLogo />}
      TextComponent={
        <NormalText>
          You earned 10 bonus points for rating 3 articles in one day. Great
          work!
        </NormalText>
      }
      primaryButton={journeyButtonProps}
    />
  )
}
