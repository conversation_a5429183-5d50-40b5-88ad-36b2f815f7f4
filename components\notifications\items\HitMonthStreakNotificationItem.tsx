import { GenericNotificationItem } from "./GenericNotificationItem"
import { HitMonthStreakNotification } from "@/apiQueries/notificationTypes"
import { NormalText } from "@/components/StyledText"
import { journeyButtonProps } from "../buttonProps"
import { InPressLogo } from "../InPressLogo"

export function HitMonthStreakNotificationItem({
  item,
}: {
  item: HitMonthStreakNotification
}) {
  return (
    <GenericNotificationItem
      timestamp={item.createdAt}
      LogoComponent={<InPressLogo />}
      TextComponent={
        <NormalText>
          Amazing! You've reached a 1-month streak and earned 300 points. Keep
          it alive for bigger rewards!
        </NormalText>
      }
      primaryButton={journeyButtonProps}
    />
  )
}
