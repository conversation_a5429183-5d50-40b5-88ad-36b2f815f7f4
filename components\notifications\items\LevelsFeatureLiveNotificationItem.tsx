import { GenericNotificationItem } from "./GenericNotificationItem"
import { LevelsFeatureLiveNotification } from "@/apiQueries/notificationTypes"
import { NormalText } from "@/components/StyledText"
import { journeyButtonProps } from "../buttonProps"
import { InPressLogo } from "../InPressLogo"

export function LevelsFeatureLiveNotificationItem({
  item,
}: {
  item: LevelsFeatureLiveNotification
}) {
  return (
    <GenericNotificationItem
      timestamp={item.createdAt}
      LogoComponent={<InPressLogo />}
      TextComponent={
        <NormalText>
          InScore Levels are now live! Rate articles to level up, unlock more
          Leads, and earn better Matches!
        </NormalText>
      }
      primaryButton={journeyButtonProps}
    />
  )
}
