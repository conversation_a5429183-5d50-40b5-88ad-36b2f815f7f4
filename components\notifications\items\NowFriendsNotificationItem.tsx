import {
  FriendRequestAcceptedNotification,
  NewFriendRequestNotification,
} from "@/apiQueries/notificationTypes"
import { Image } from "expo-image"
import { GenericNotificationItem } from "./GenericNotificationItem"
import { styles } from "../notificationStyles"
import { BoldText, NormalText } from "@/components/StyledText"

export function NowFriendsNotificationItem({
  item,
  onOpenChat,
}: {
  item: NewFriendRequestNotification | FriendRequestAcceptedNotification
  onOpenChat: () => void
}) {
  const ImageComponent = (
    <Image
      source={{ uri: item.user.image.url }}
      placeholder="LKO2?U%2Tw=w]~RBVZRi}RPxYJt6"
      contentFit="cover"
      contentPosition="center"
      style={styles.image}
    />
  )

  const userIsActive = !item.user.isArchived

  return (
    <GenericNotificationItem
      timestamp={item.createdAt}
      LogoComponent={ImageComponent}
      TextComponent={
        <NormalText>
          You and{" "}
          <BoldText>
            {item.user.firstName} {item.user.lastName}
          </BoldText>{" "}
          are now friends
        </NormalText>
      }
      secondaryButton={{
        text: "Open chat",
        onPress: onOpenChat,
      }}
      disabled={!userIsActive}
    />
  )
}
