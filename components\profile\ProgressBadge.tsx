import { Text, View } from "react-native"
import _ from "lodash"
import { abbreviateNumber } from "@/utils/numberAbbreviation"
import { useStyles } from "./Badge"
import { Image } from "expo-image"
import { Level } from "@/types/levels"

interface ProgressBadgeProps {
  initialLevel: Level | undefined
  initialNextLevel: Level | undefined
  points: number
}

export const ProgressBadge = ({
  initialLevel,
  initialNextLevel,
  points,
}: ProgressBadgeProps) => {
  const styles = useStyles(
    initialNextLevel?.color || initialLevel?.color || "#ccc",
  )

  const iconUrl = initialNextLevel?.iconUrl || initialLevel?.iconUrl || ""

  const pointsToShow = initialNextLevel
    ? _.min([points, initialNextLevel.pointsRequired])!
    : points

  return (
    <View style={styles.progressOuterContainer}>
      <View style={styles.nextLevelTextContainer}>
        {initialNextLevel && (
          <Text style={styles.nextLevelText}>
            Level {initialNextLevel.place}: {initialNextLevel.name}
          </Text>
        )}
      </View>
      <View
        style={[
          styles.container,
          styles.progressContainer,
          {
            paddingLeft: initialNextLevel ? 10 : 10,
            paddingRight: initialNextLevel ? 15 : 10,
          },
        ]}
      >
        <Image source={{ uri: iconUrl }} style={styles.iconImage} />
        {initialNextLevel ? (
          <Text style={[styles.progressPoints, styles.boldPoints]}>
            {abbreviateNumber(pointsToShow)}/
            {abbreviateNumber(initialNextLevel.pointsRequired)}
          </Text>
        ) : (
          <Text style={[styles.progressPoints, styles.boldPoints]}>
            {points.toLocaleString()}
          </Text>
        )}
      </View>
    </View>
  )
}
