import { useEffect, useState } from "react"
import { Text, View, StyleSheet } from "react-native"
import { CameraView, useCameraPermissions } from "expo-camera"
import {
  heightPercentageToDP as hp,
  widthPercentageToDP as wp,
} from "react-native-responsive-screen"
import ScannedMatchModal from "@/components/leads/ScannedMatchModal"
import { BEIGE } from "@/constants/Colors"

const QrCodeScanner = () => {
  const [permission, requestPermission] = useCameraPermissions()
  const [otherUserId, setOtherUserId] = useState<number | null>(null)
  const [showMatchFriendModal, setShowMatchFriendModal] = useState(false)

  useEffect(() => {
    if (!permission) {
      requestPermission()
    }
  }, [permission])

  if (!permission) {
    return <View />
  }

  if (!permission.granted) {
    return (
      <View style={styles.container}>
        <Text style={styles.message}>
          We need your permission to show the scanner view
        </Text>
      </View>
    )
  }

  if (showMatchFriendModal && otherUserId) {
    return (
      <ScannedMatchModal
        visible={showMatchFriendModal}
        otherUserId={otherUserId}
        onClose={() => {
          setShowMatchFriendModal(false)
        }}
      />
    )
  }

  return (
    <View>
      <CameraView
        style={styles.cameraStyle}
        facing="back"
        barcodeScannerSettings={{
          barcodeTypes: ["qr"],
        }}
        onBarcodeScanned={({ data: userId }: { data: string }) => {
          setOtherUserId(parseInt(userId))
          setTimeout(() => {
            setShowMatchFriendModal(true)
          }, 500)
        }}
      >
        <View style={styles.qrScannerContainer}>
          <View style={styles.outerTransparentContainer} />
          <View style={styles.innerTransparentContainer}>
            <View style={styles.outerTransparentContainer} />
            <View style={styles.scanningIconContainer}>
              <View style={styles.scanTextContainer}>
                <Text style={styles.scanText}>Scan QR Code</Text>
              </View>
            </View>
            <View style={styles.outerTransparentContainer} />
          </View>
          <View style={styles.outerTransparentContainer} />
        </View>
      </CameraView>
    </View>
  )
}

export default QrCodeScanner

const styles = StyleSheet.create({
  scanTextContainer: {
    position: "absolute",
    bottom: -80,
    left: "50%",
    transform: [{ translateX: -90 }],
    width: 180,
    height: 40,
    borderRadius: 15,
    backgroundColor: "rgba(0, 0, 0, 0.8)",
    justifyContent: "center",
  },
  scanText: {
    color: "white",
    fontSize: 18,
    textAlign: "center",
    fontWeight: "500",
  },
  qrScannerContainer: {
    height: hp(100),
    width: wp(100),
    flex: 1,
  },
  innerTransparentContainer: {
    flexDirection: "row",
  },
  outerTransparentContainer: {
    flexGrow: 1,
    flexDirection: "row",
    backgroundColor: "rgba(255, 255, 255, 0.2)",
  },
  scanningIconContainer: {
    height: wp(70),
    width: wp(70),
    borderColor: "white",
    borderWidth: 4,
    borderRadius: 15,
  },
  container: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: BEIGE,
  },
  message: {
    textAlign: "center",
    paddingBottom: 10,
  },
  cameraStyle: {
    width: wp(100),
    height: hp(100),
    justifyContent: "center",
    alignItems: "center",
  },
  textStyle: {
    color: "black",
    fontSize: 18,
    textAlign: "center",
  },
  scanningIconStyle: {
    width: wp(70),
    height: wp(70),
    tintColor: "white",
  },
})
