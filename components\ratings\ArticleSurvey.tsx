import { FeelingsMenu } from "./FeelingsMenu"
import { ScaleRating } from "./ScaleRating"
import { useEffect, useState } from "react"
import { SurveyStartButton } from "./SurveyStartButton"
import { Dimensions, StyleSheet, View } from "react-native"
import { LinearGradient } from "expo-linear-gradient"
import { PointsCompletionStep } from "./PointsCompletionStep"
import { RatingDrawer } from "./RatingDrawer"
import { SurveyResponse } from "@/apiQueries/apiQueries"
import { CompletionStep } from "./CompletionStep"
import _ from "lodash"
import { levelsSoundAssets } from "@/utils/sounds"
import { useFeatureFlag } from "@/utils/featureFlags"
import { PointEvent } from "@/types/levels"
import { LeaderboardUser } from "@/types/user"

export interface FinishedSurvey {
  feelings: string[]
  importanceRating: number
  interestRating: number
}

type NewSurvey = Partial<FinishedSurvey>

export interface ArticleSurveyProps {
  initialPoints: number
  soundsAndHapticsOn: boolean
  onSurveyComplete: (survey: FinishedSurvey) => Promise<SurveyResponse>
  onFetchLeaderboard: () => Promise<LeaderboardUser[]>
}

export interface Step {
  title?: string
  subtitle?: string
  nextIsDisabled?: boolean
  isSubmitStep?: boolean
  children: JSX.Element
}

const { width, height } = Dimensions.get("window")

export const ArticleSurvey = ({
  initialPoints,
  soundsAndHapticsOn,
  onSurveyComplete,
  onFetchLeaderboard,
}: ArticleSurveyProps) => {
  const showLevelsSteps = useFeatureFlag("levels")
  const soundHapticsFlag = useFeatureFlag("rating_sounds_and_haptics")

  const [pointEvents, setPointEvents] = useState<PointEvent[] | undefined>()
  const [leaderboardUsers, setLeaderboardUsers] = useState<
    LeaderboardUser[] | undefined
  >()
  const [isExpanded, setIsExpanded] = useState(false)
  const [stepIndex, setStepIndex] = useState(0)
  const [survey, setSurvey] = useState<NewSurvey>({
    feelings: [],
    importanceRating: undefined,
    interestRating: undefined,
  })

  const loadSounds = async () => {
    levelsSoundAssets.forEach((a) => {
      a.sound.loadAsync(a.source, { shouldPlay: false }, false)
    })
  }

  const handleNext = () => {
    setStepIndex(stepIndex + 1)
  }

  const handleBack = () => {
    setStepIndex(stepIndex - 1)
  }

  const handleSubmit = async () => {
    const { pointEvents } = await onSurveyComplete(survey as FinishedSurvey)
    setPointEvents(pointEvents)

    const leaderboardUsers = await onFetchLeaderboard()
    setLeaderboardUsers(leaderboardUsers)
  }

  useEffect(() => {
    if (soundHapticsFlag) {
      loadSounds()
      return () => {
        levelsSoundAssets.forEach((a) => {
          a.sound.unloadAsync()
        })
      }
    }
  }, [])

  useEffect(() => {
    if (steps[stepIndex].isSubmitStep) handleSubmit()
  }, [stepIndex])

  const handleClose = () => {
    setIsExpanded(false)
    setStepIndex(0)
    setSurvey({
      feelings: [],
      importanceRating: undefined,
      interestRating: undefined,
    })
  }

  const ratingSubtitle = "Select the option that best fits."
  const ratingOptions = [
    "Not at all",
    "Slightly",
    "Somewhat",
    "Moderately",
    "Very",
  ]

  const steps: Step[] = [
    {
      title: "How did this article make you feel?",
      subtitle: "Select all that apply.",
      nextIsDisabled: survey.feelings?.length === 0,
      children: (
        <FeelingsMenu
          initialFeelings={survey.feelings || []}
          onChange={(feelings) => setSurvey({ ...survey, feelings })}
        />
      ),
    },
    {
      title: "How important is this to you?",
      subtitle: ratingSubtitle,
      nextIsDisabled: survey.importanceRating === undefined,
      children: (
        <ScaleRating
          key="importanceRating"
          initialValue={survey.importanceRating}
          options={ratingOptions}
          onChange={(importanceRating) =>
            setSurvey({ ...survey, importanceRating })
          }
        />
      ),
    },
    {
      title: "How interesting is this to you?",
      subtitle: ratingSubtitle,
      nextIsDisabled: survey.interestRating === undefined,
      children: (
        <ScaleRating
          key="interestRating"
          initialValue={survey.interestRating}
          options={ratingOptions}
          onChange={(interestRating) =>
            setSurvey({ ...survey, interestRating })
          }
        />
      ),
    },
    showLevelsSteps
      ? {
          isSubmitStep: true,
          children: (
            <PointsCompletionStep
              initialPoints={initialPoints}
              pointEvents={pointEvents}
              soundAssets={levelsSoundAssets}
              soundAndHapticsOn={soundsAndHapticsOn}
              leaderboardUsers={leaderboardUsers}
            />
          ),
        }
      : { isSubmitStep: true, children: <CompletionStep /> },
  ]

  return (
    <>
      {isExpanded && (
        <LinearGradient
          style={styles.viewPortGradient}
          colors={[
            "rgba(0, 0, 0, 0.2)",
            "rgba(0, 0, 0, 0.6)",
            "rgba(0, 0, 0, 0.75)",
            "rgba(0, 0, 0, 0.85)",
          ]}
        />
      )}
      {!isExpanded ? (
        <View style={styles.container}>
          <SurveyStartButton
            onPress={() => {
              setIsExpanded(true)
            }}
          />
        </View>
      ) : (
        <RatingDrawer
          step={steps[stepIndex]}
          stepIndex={stepIndex}
          totalSteps={steps.length - 1}
          onNext={
            steps[stepIndex].isSubmitStep || stepIndex === steps.length - 1
              ? undefined
              : handleNext
          }
          onBack={
            !steps[stepIndex].isSubmitStep &&
            stepIndex > 0 &&
            stepIndex < steps.length - 1
              ? handleBack
              : undefined
          }
          onClose={handleClose}
        />
      )}
    </>
  )
}

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    justifyContent: "center",
    marginTop: -17,
    paddingTop: 15,
    paddingBottom: 20,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    backgroundColor: "white",
    width: "100%",
    shadowOpacity: 0.15,
    shadowRadius: 5,
    shadowOffset: { width: 0, height: -5 },
    elevation: 5,
  },

  viewPortGradient: {
    position: "absolute",
    top: 0,
    right: 0,
    left: 0,
    width,
    height,
  },
})
