import { Text } from "../Themed"
import { StyleSheet, View } from "react-native"
import { completionStepStyles } from "./styles"
import { CheckProgressButton } from "./CheckProgressButton"
import { Button } from "react-native-paper"
import { Image } from "expo-image"

type LevelUpProps = {
  imageUrl?: string
  title: string
  subtitle: string
  children?: React.ReactNode
  handleShare?: () => void
}

export const CelebrateStep = ({
  imageUrl,
  title,
  subtitle,
  children,
  handleShare,
}: LevelUpProps) => {
  return (
    <View style={[completionStepStyles.container, { marginBottom: 32 }]}>
      {imageUrl && <Image source={{ uri: imageUrl }} style={styles.badge} />}
      <Text style={[completionStepStyles.title, { marginBottom: 20 }]}>
        {title}
      </Text>
      <Text
        style={[
          styles.subtitle,
          { marginBottom: imageUrl || children ? 30 : 40 },
        ]}
      >
        {subtitle}
      </Text>
      {children}
      <CheckProgressButton />
      {handleShare && (
        <Button
          mode="text"
          textColor="black"
          labelStyle={styles.shareButton}
          onPress={handleShare}
        >
          Share
        </Button>
      )}
    </View>
  )
}

const styles = StyleSheet.create({
  badge: {
    width: 87,
    height: 100,
    marginBottom: 26,
  },
  subtitle: {
    fontFamily: "Inter-Regular",
    fontSize: 16,
    textAlign: "center",
    marginBottom: 30,
  },
  shareButton: {
    fontSize: 16,
    fontFamily: "InterTight-SemiBold",
  },
})
