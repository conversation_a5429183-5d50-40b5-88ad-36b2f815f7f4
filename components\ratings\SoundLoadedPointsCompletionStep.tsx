import { useEffect, useState } from "react"
import {
  PointsCompletionStep,
  PointsCompletionStepProps,
} from "./PointsCompletionStep"
import { levelsSoundAssets } from "@/utils/sounds"

export const SoundLoadedPointsCompletionStep = (
  props: Omit<PointsCompletionStepProps, "soundAssets" | "soundAndHapticsOn">,
) => {
  const [soundsLoaded, setSoundsLoaded] = useState(false)

  const loadSounds = async () => {
    for (const asset of levelsSoundAssets) {
      await asset.sound.loadAsync(asset.source, { shouldPlay: false }, false)
    }
  }

  useEffect(() => {
    loadSounds().then(() => {
      setSoundsLoaded(true)
    })
    return () => {
      levelsSoundAssets.forEach((a) => {
        a.sound.unloadAsync()
      })
    }
  })

  if (!soundsLoaded) return null
  return (
    <PointsCompletionStep
      {...props}
      soundAssets={levelsSoundAssets}
      soundAndHapticsOn
    />
  )
}
