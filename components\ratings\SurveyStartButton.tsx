import { StyleSheet } from "react-native"
import { Button } from "../Button"
import StartSurveyIcon from "../icons/StartSurvey"

export const SurveyStartButton = ({ onPress }: { onPress: () => void }) => {
  return (
    <Button
      text="Rate this article"
      iconComponent={<StartSurveyIcon style={styles.icon} />}
      isTextOnLeft={false}
      onPress={onPress}
      style={styles.container}
      textStyle={styles.text}
    />
  )
}
const styles = StyleSheet.create({
  container: {
    paddingVertical: 9.5,
    backgroundColor: "black",
    borderRadius: 30,
    height: "auto",
    alignSelf: "center",
  },
  text: {
    fontSize: 13,
    letterSpacing: 0.39,
    fontFamily: "InterTight-Regular",
  },
  icon: {
    width: 24,
    height: 24,
  },
})
