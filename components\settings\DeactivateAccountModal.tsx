import React, { useMemo, useState } from "react"
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  StyleSheet,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
} from "react-native"
import { TextInput } from "../TextInput"
import { modalStyles } from "@/styles"
import _ from "lodash"
import { Button } from "../Button"

type Deactivation = {
  reasons: string[]
  comment: string
}

type DeactivateAccountModalProps = {
  visible: boolean
  onDeactivate: (d: Deactivation) => void
  onClose: () => void
}

enum Reason {
  not_enough_leads = "There weren't enough Leads",
  not_enough_matches = "I didn't get enough Matches",
  news_not_relevant = "The news wasn't relevant to me",
  didnt_like_leads = "I didn't like the Leads I had",
  technical_issues = "I experienced technical issues",
  other = "Something else",
}

const DeactivateAccountModal = ({
  visible,
  onDeactivate,
  onClose,
}: DeactivateAccountModalProps) => {
  const [selectedReasons, setSelectedReasons] = useState<Reason[]>([])
  const [comment, setComment] = useState<string>("")

  const reasonOptions: Reason[] = [
    Reason.not_enough_leads,
    Reason.not_enough_matches,
    Reason.news_not_relevant,
    Reason.didnt_like_leads,
    Reason.technical_issues,
    Reason.other,
  ]

  const toggleReason = (reason: Reason) => {
    if (selectedReasons.includes(reason)) {
      setSelectedReasons((previousReasons) =>
        previousReasons.filter((r) => r !== reason),
      )
    } else {
      setSelectedReasons((previousReasons) => [...previousReasons, reason])
    }
  }

  const additionalCommentsAllowed = useMemo(
    () =>
      selectedReasons.some((reason) =>
        [Reason.other, Reason.technical_issues].includes(reason),
      ),
    [selectedReasons],
  )

  const handleDeactivation = () => {
    onDeactivate({
      reasons: selectedReasons,
      comment,
    })
  }

  return (
    <Modal visible={visible} transparent={true} animationType="slide">
      <View style={modalStyles.modalContainer}>
        <KeyboardAvoidingView
          style={[modalStyles.modalContent, { height: "auto" }]}
          behavior={Platform.OS === "ios" ? "padding" : undefined}
        >
          <ScrollView
            contentContainerStyle={modalStyles.scrollView}
            showsVerticalScrollIndicator={false}
            keyboardShouldPersistTaps="handled"
          >
            <Text style={[modalStyles.header, { textAlign: "center" }]}>
              Well, it can't all be good news
            </Text>
            <Text style={[modalStyles.subHeader, styles.subHeader]}>
              We'd love to know why you're deleting your account.
            </Text>
            <View
              style={[modalStyles.reasonsContainer, styles.reasonsContainer]}
            >
              {reasonOptions.map((option) => (
                <TouchableOpacity
                  key={option}
                  style={[
                    modalStyles.reasonButton,
                    selectedReasons.includes(option) &&
                      modalStyles.selectedReason,
                  ]}
                  onPress={() => toggleReason(option)}
                >
                  <Text
                    style={[
                      modalStyles.reasonText,
                      styles.reasonText,
                      selectedReasons.includes(option) &&
                        modalStyles.selectedReasonText,
                    ]}
                  >
                    {option}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>

            {additionalCommentsAllowed && (
              <>
                <Text style={modalStyles.reasonLabel}>Additional Comments</Text>
                <TextInput
                  inputStyle={modalStyles.input}
                  placeholder="Any additional feedback for us? Please be honest!"
                  value={comment}
                  numberOfLines={4}
                  onChangeText={setComment}
                  multiline
                />
              </>
            )}

            <Button
              style={{ ...modalStyles.button, ...styles.button }}
              onPress={handleDeactivation}
              textStyle={modalStyles.buttonText}
              text="Deactivate Account"
            />

            <Button
              style={{ ...modalStyles.button, ...modalStyles.cancelButton }}
              textStyle={modalStyles.cancelButtonText}
              onPress={onClose}
              text="Cancel"
            />
          </ScrollView>
        </KeyboardAvoidingView>
      </View>
    </Modal>
  )
}

const styles = StyleSheet.create({
  subHeader: {
    textAlign: "center",
    paddingHorizontal: 20,
  },
  reasonsContainer: {
    flexDirection: "column",
  },
  reasonText: {
    flex: 1,
    textAlign: "center",
  },
  button: {
    width: "100%",
    backgroundColor: "#F14826",
  },
})

export default DeactivateAccountModal

export { Deactivation }
