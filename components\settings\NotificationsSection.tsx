import { useEffect, useState } from "react"
import { <PERSON><PERSON><PERSON>, ProfileList } from "./ProfileItems"
import { <PERSON>ton, Switch } from "react-native-paper"
import { BROWNSTONE } from "@/constants/Colors"
import {
  checkIfNotificationsEnabled,
  registerForPushNotificationsAsync,
} from "@/utils/pushNotifications"
import { openSystemSettings } from "@/utils/permissions"
import { useFocusEffect } from "expo-router"
import { Session } from "@/types/user"
import { updatePushToken } from "@/apiQueries/auth"

export default function NotificationsSection({
  session,
  onToggleOptOut,
}: {
  session: Session
  onToggleOptOut: (value: boolean) => void
}) {
  const [optedOut, setOptedOut] = useState<boolean | undefined>(
    session.user.settings?.nonessentialNotificationsDisabled,
  )
  const [enabled, setEnabled] = useState<boolean | undefined>()

  const checkAndUpdateStatus = async () => {
    checkIfNotificationsEnabled().then(async (enabled) => {
      if (enabled) {
        const pushToken = await registerForPushNotificationsAsync()
        if (!pushToken) {
          return
        }

        await updatePushToken({ token: session.token, pushToken })
      }
      return setEnabled(!!enabled)
    })
  }

  const handleOptOut = async (value: boolean) => {
    setOptedOut(value)
    onToggleOptOut(value)
  }

  useEffect(() => {
    checkAndUpdateStatus()
  }, [])

  useFocusEffect(() => {
    checkAndUpdateStatus()
  })

  const permissionsItem: ProfileItem = {
    title: "Notification permissions missing",
    subtitle: "You're missing out on messages!",
    endComponent: (
      <Button
        textColor="red"
        style={{ height: 40 }}
        onPress={() => openSystemSettings()}
        mode="text"
      >
        Open settings
      </Button>
    ),
  }

  const toggleItem: ProfileItem = {
    title: "Disable nonessential notifications",
    subtitle: "Only get match and message alerts",
    endComponent: (
      <Switch
        color={BROWNSTONE}
        value={optedOut}
        onValueChange={handleOptOut}
      />
    ),
  }

  const items = enabled === false ? [permissionsItem, toggleItem] : [toggleItem]

  return <ProfileList items={items} />
}

export { checkIfNotificationsEnabled as areNotificationsEnabled }
