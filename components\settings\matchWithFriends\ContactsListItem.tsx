import { View, Text, StyleSheet } from "react-native"
import { Contact } from "expo-contacts"
import { Image } from "expo-image"
import { TouchableOpacity } from "react-native-gesture-handler"
import { LIME_GREEN } from "@/constants/Colors"

export default function ContactsListItem({
  contact,
  buttonText,
  onPress,
  isSelected,
}: {
  contact: Contact
  buttonText: string
  onPress?: () => void
  isSelected?: boolean
}) {
  const initials = contact.name
    .split(" ")
    .map((name) => name.at(0)?.toUpperCase())
    .filter((char) => char !== undefined)
    .slice(0, 2)
    .join("")

  const phoneNumber = contact.phoneNumbers?.at(0)?.number

  let touchableStyle

  if (isSelected) {
    touchableStyle = {
      backgroundColor: "white",
      borderColor: "black",
      borderWidth: 0.5,
    }
  } else if (onPress) {
    touchableStyle = {
      backgroundColor: "black",
    }
  } else {
    touchableStyle = {
      backgroundColor: "transparent",
      borderColor: "black",
      borderWidth: 0.5,
    }
  }

  let textStyle

  if (isSelected) {
    textStyle = {
      color: "black",
    }
  } else if (onPress) {
    textStyle = {
      color: "white",
    }
  } else {
    textStyle = {
      color: "black",
    }
  }

  return (
    <View style={styles.container}>
      <View style={styles.innerContainer}>
        {contact.imageAvailable ? (
          <Image
            source={{
              uri: contact.image?.uri,
            }}
            style={styles.avatarContainer}
          />
        ) : (
          <View style={styles.avatarContainer}>
            <Text style={styles.initials}>{initials}</Text>
          </View>
        )}
        <View>
          <Text style={styles.name}>{contact.name}</Text>
          {<Text style={styles.phoneNumber}>{phoneNumber}</Text>}
        </View>
      </View>
      <TouchableOpacity
        onPress={onPress}
        style={[styles.button, touchableStyle]}
      >
        <Text style={textStyle}>{buttonText}</Text>
      </TouchableOpacity>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
  },
  innerContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 12,
  },
  avatarContainer: {
    width: 56,
    height: 56,
    backgroundColor: LIME_GREEN,
    borderRadius: 28,
    alignItems: "center",
    justifyContent: "center",
  },
  initials: { fontSize: 20, color: "black" },
  name: { fontSize: 16 },
  phoneNumber: { fontSize: 12 },
  button: {
    paddingHorizontal: 10,
    paddingVertical: 6,
    borderRadius: 20,
  },
})
