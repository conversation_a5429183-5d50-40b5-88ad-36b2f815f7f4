import { StyleSheet, View } from "react-native"
import { Text } from "../Themed"
import { Slider } from "../widgets/Slider"

export interface AgePreferenceSelectorProps {
  minAgePref?: number
  maxAgePref?: number
  onChange: (values: number[]) => void
}

export const Marker = ({
  currentValue,
  showValue = true,
  disabled = false,
}: {
  currentValue: number
  showValue?: boolean
  disabled?: boolean
}) => {
  return (
    <View>
      <View
        style={[
          styles.markerCircle,
          {
            backgroundColor: disabled ? "lightgray" : "#C16449",
            marginBottom: showValue ? 10 : 26,
          },
        ]}
      />
      {showValue && <Text style={styles.markerText}>{currentValue}</Text>}
    </View>
  )
}

export const AgePreferenceSelector = ({
  minAgePref,
  maxAgePref,
  onChange,
}: AgePreferenceSelectorProps) => {
  return (
    <Slider
      min={18}
      max={99}
      step={1}
      smoothSnapped={true}
      values={[minAgePref || 18, maxAgePref || 100]}
      customMarker={Marker}
      onValuesChangeFinish={onChange}
    />
  )
}

const styles = StyleSheet.create({
  markerCircle: {
    marginTop: 30,
    borderRadius: 50,
    width: 24,
    height: 24,
    marginBottom: 26,
  },
  markerText: {
    textAlign: "center",
    fontFamily: "InterTight-SemiBold",
    fontWeight: "500",
    fontSize: 12,
  },
})
