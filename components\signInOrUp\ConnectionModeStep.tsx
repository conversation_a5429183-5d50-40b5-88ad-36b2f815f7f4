import { StyleSheet, TouchableOpacity, View } from "react-native"
import React from "react"
import { Text } from "../Themed"
import { BROWNSTONE } from "@/constants/Colors"
import { RadioButton } from "react-native-paper"

export enum ConnectionMode {
  Both = "both",
  Dates = "dates",
  Friends = "friends",
}

interface ConnectionModePrefProps {
  connectionMode: ConnectionMode
  onChange: (connectionMode: ConnectionMode) => void
}

export const connectionPrefOptions: { value: ConnectionMode; label: string }[] =
  [
    {
      value: ConnectionMode.Both,
      label: "Dates + Friends",
    },
    {
      value: ConnectionMode.Dates,
      label: "Dates only",
    },
    {
      value: ConnectionMode.Friends,
      label: "Friends only",
    },
  ]

export const ConnectionModeStep = ({
  connectionMode,
  onChange,
}: ConnectionModePrefProps) => {
  return (
    <View style={styles.container}>
      {connectionPrefOptions.map(({ value, label }) => (
        <TouchableOpacity
          style={styles.section}
          key={label}
          activeOpacity={0.8}
          onPress={() => onChange(value)}
        >
          <Text style={styles.paragraph}>{label}</Text>
          <RadioButton
            color={BROWNSTONE}
            value={value}
            onPress={() => onChange(value)}
            status={connectionMode == value ? "checked" : "unchecked"}
          />
        </TouchableOpacity>
      ))}
    </View>
  )
}

export const styles = StyleSheet.create({
  container: {
    display: "flex",
    flexDirection: "column",
    gap: 8,
  },
  section: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    backgroundColor: "white",
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "rgba(0,0,0,0.2)",
  },
  paragraph: {
    fontSize: 15,
  },
})
