import { TouchableOpacity, View } from "react-native"
import React from "react"
import { Text } from "../Themed"
import Checkbox from "expo-checkbox"
import { BROWNSTONE } from "@/constants/Colors"
import { styles } from "./ConnectionModeStep"

export type GenderPref = "male" | "female" | "nonbinary"
export type GenderPrefs = GenderPref[]

interface GenderPrefStepProps {
  genderPrefs: GenderPrefs
  onChange: (genderPrefs: GenderPrefs) => void
}

export const genderPrefOptions: { value: GenderPref; label: string }[] = [
  {
    value: "male",
    label: "Men",
  },
  {
    value: "female",
    label: "Women",
  },
  {
    value: "nonbinary",
    label: "Non-binary",
  },
]

export const GenderPrefStep = ({
  genderPrefs,
  onChange,
}: GenderPrefStepProps) => {
  const isGenderPrefSelected = (genderPref: GenderPref) =>
    genderPrefs?.includes(genderPref)

  const toggleGenderPref = (genderPref: GenderPref) => {
    onChange(
      !isGenderPrefSelected(genderPref)
        ? [...genderPrefs, genderPref]
        : genderPrefs.filter((pref) => pref !== genderPref),
    )
  }

  return (
    <View style={styles.container}>
      {genderPrefOptions.map(({ value: genderPref, label: isPreferred }) => (
        <TouchableOpacity
          style={styles.section}
          key={genderPref}
          onPress={() => toggleGenderPref(genderPref)}
          activeOpacity={0.8}
        >
          <Text style={styles.paragraph}>{isPreferred}</Text>
          <Checkbox
            color={BROWNSTONE}
            value={isGenderPrefSelected(genderPref)}
            onValueChange={() => toggleGenderPref(genderPref)}
          />
        </TouchableOpacity>
      ))}
    </View>
  )
}
