import { View, StyleSheet } from "react-native"
import { TextInput } from "../TextInput"
import { heightPercentageToDP as hp } from "react-native-responsive-screen"

interface JobStepProps {
  occupation?: string
  onChange: (occupation: string) => void
}

export const JobStep = ({ occupation, onChange }: JobStepProps) => (
  <View style={styles.container}>
    <TextInput label="Occupation" value={occupation} onChangeText={onChange} />
  </View>
)

export const styles = StyleSheet.create({
  container: {
    marginVertical: hp("3%"),
  },
})
