import { Text, View, StyleSheet, ViewStyle } from "react-native"
import { useEffect, useState } from "react"
import { Button } from "../Button"
import { requestLocationPermission } from "@/utils/location"
import { LatLng } from "react-native-maps"

interface PermissionStepProps {
  getUserPosition: () => Promise<LatLng>
  onPositionChange: (position: LatLng) => void
  onPressEnable: () => void
}

const PermissionStep = ({
  getUserPosition,
  onPositionChange,
  onPressEnable,
}: PermissionStepProps) => {
  const [hasPermission, setHasPermission] = useState<boolean | undefined>()

  useEffect(() => {
    checkPermissionsAndLocation()
  }, [])

  const checkPermissionsAndLocation = async () => {
    const permissionStatus = await requestLocationPermission()
    if (permissionStatus === "granted") {
      setHasPermission(true)
      const position = await getUserPosition()
      onPositionChange(position)
    } else {
      setHasPermission(false)
    }
  }

  const handlePress = async () => {
    await checkPermissionsAndLocation()

    if (hasPermission) {
      onPressEnable()
    }
  }

  return (
    <View style={styles.container}>
      <Button text="Continue" onPress={handlePress} />

      {hasPermission === false ? (
        <View>
          <Text style={styles.error}>
            We couldn't get permission to check your location. To enable
            permissions, go to your phone's system settings and enable location
            permissions for InPress.
          </Text>
        </View>
      ) : null}
    </View>
  )
}

const bypassBox: ViewStyle = {
  position: "absolute",
  top: 0,
  width: 150,
  height: 250,
  zIndex: 5,
}

const styles = StyleSheet.create({
  container: {
    gap: 32,
  },
  error: {
    fontSize: 20,
    color: "red",
  },
})
export default PermissionStep
