import { StyleSheet, Text, TouchableOpacity } from "react-native"
import React, { useEffect, useState } from "react"
import { Screen } from "../../Themed"
import { TextInput } from "../../TextInput"
import { NewResponse } from "./PromptStep_"
import { Image } from "react-native"

const MAX_ANSWER_CHAR_LIMIT = 150

interface FillPromptProps {
  initialResponse: NewResponse
  onChangeResponse: (promptResponse: NewResponse) => void
  onChangePrompt: (promptResponse: NewResponse) => void
}

const FillPrompt = ({
  initialResponse,
  onChangeResponse,
  onChangePrompt,
}: FillPromptProps) => {
  const [promptResponse, setPromptResponse] =
    useState<NewResponse>(initialResponse)

  useEffect(() => {
    onChangeResponse(promptResponse)
  }, [promptResponse])

  const TextWordCounter = () => {
    return (
      <Text style={styles.wordCounterText}>
        {MAX_ANSWER_CHAR_LIMIT - (promptResponse.text || "").length} characters
        remaining
      </Text>
    )
  }

  const EditPromptButton = () => {
    return (
      <TouchableOpacity onPress={() => onChangePrompt(promptResponse)}>
        <Image
          source={require("../assets/pencil-line.png")}
          style={styles.pencilIcon}
        />
      </TouchableOpacity>
    )
  }

  return (
    <Screen style={styles.container}>
      <TextInput
        value={promptResponse.prompt}
        rightComponent={<EditPromptButton />}
        inputStyle={styles.inputStyle}
        inputContainerStyle={styles.inputContainerStyle}
        editable={false}
      />
      <TextInput
        value={promptResponse.text}
        onChangeText={(text) => setPromptResponse({ ...promptResponse, text })}
        inputContainerStyle={styles.answerInputContainerStyle}
        multiline
        inputStyle={[styles.inputStyle, styles.answerInputStyle]}
        rightComponent={<TextWordCounter />}
        maxLength={MAX_ANSWER_CHAR_LIMIT}
      />
    </Screen>
  )
}

export default FillPrompt

const styles = StyleSheet.create({
  container: {
    paddingTop: 24,
  },
  inputStyle: {
    fontFamily: "InterTight-Regular",
    color: "#000000",
    fontSize: 16,
  },
  inputContainerStyle: {
    paddingHorizontal: 18,
    marginBottom: 8,
    alignItems: "center",
  },
  answerInputContainerStyle: {
    paddingTop: 8,
    paddingHorizontal: 18,
    flexDirection: "column",
    alignItems: "flex-start",
    height: 120,
  },
  answerInputStyle: {
    width: "100%",
    textAlignVertical: "top",
  },
  pencilIcon: {
    height: 24,
    width: 24,
  },
  wordCounterText: {
    alignSelf: "flex-end",
    marginBottom: 16,
    color: "#000000",
    opacity: 0.6,
    fontFamily: "InterTight-Regular",
  },
})
