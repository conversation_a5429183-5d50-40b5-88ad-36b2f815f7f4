import { Prompt } from "../SignUp"
import React, { useEffect, useState } from "react"
import { fetchPrompts } from "@/apiQueries/auth"
import _ from "lodash"
import { ActiveConnectionMode } from "@/context/ModeContext"
import PromptStep_, { PromptStepProps_ } from "./PromptStep_"

interface PromptStepProps extends Omit<PromptStepProps_, "promptChoices"> {
  connectionMode: ActiveConnectionMode
}

const PromptStep = ({ connectionMode, ...props }: PromptStepProps) => {
  const [promptChoices, setPromptChoices] = useState<Prompt[]>([])

  useEffect(() => {
    const getPrompts = async () => {
      try {
        const result = await fetchPrompts(connectionMode)
        setPromptChoices(_.sampleSize(result, 10))
      } catch (error) {
        console.error(error)
      }
    }
    getPrompts()
  }, [])

  return <PromptStep_ {...props} promptChoices={promptChoices} />
}

export default PromptStep
