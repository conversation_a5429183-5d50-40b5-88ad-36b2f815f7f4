import Colors from "@/constants/Colors"
import { Ionicons } from "@expo/vector-icons"
import { StyleSheet, TouchableOpacity, View, Text } from "react-native"
import { NewResponse } from "./PromptStep_"
import { MIN_PROMPTS } from "../constants"
import { isCompleteResponse } from "./utils"

interface ResponsesProps {
  responses: NewResponse[]
  onPressResponse?: (response: NewResponse) => void
  clearPrompt?: (index: number) => void
}

export const Responses = ({
  responses,
  onPressResponse,
  clearPrompt,
}: ResponsesProps) => {
  const renderResponses = (response: NewResponse, index: number) => {
    return (
      <TouchableOpacity
        style={[
          styles.promptContainer,
          response.prompt ? styles.selectedPromptContainer : {},
        ]}
        disabled={!onPressResponse}
        onPress={() => onPressResponse && onPressResponse(response)}
        key={response.position}
      >
        <View style={styles.promptTextContainer}>
          <Text
            style={[
              styles.nonSelectedText,
              response.prompt ? styles.selectedText : {},
            ]}
            numberOfLines={2}
          >
            {response.prompt ? response.prompt : "Select scoop"}
          </Text>
          <Text style={[styles.nonSelectedText]} numberOfLines={2}>
            {response.text ? response.text : "And write your answer"}
          </Text>
        </View>
        {clearPrompt && (
          <Ionicons
            name={response.prompt ? "close-circle-outline" : "add-circle"}
            color={response.prompt ? "#000000" : Colors.light.primaryColor}
            size={20}
            disabled={!response.prompt}
            onPress={() => clearPrompt(index)}
          />
        )}
      </TouchableOpacity>
    )
  }

  return (
    <View style={styles.promptListContainer}>
      {responses?.map(renderResponses)}
      {responses.filter(isCompleteResponse).length < MIN_PROMPTS && (
        <Text style={styles.notEnoughScoops}>
          You need at least {MIN_PROMPTS} scoop{MIN_PROMPTS > 1 ? "s" : ""}.
        </Text>
      )}
    </View>
  )
}

const styles = StyleSheet.create({
  promptListContainer: {
    gap: 16,
  },
  promptContainer: {
    paddingVertical: 18,
    paddingHorizontal: 16,
    borderRadius: 10,
    gap: 10,
    flexDirection: "row",
    alignItems: "center",
    borderStyle: "dashed",
    borderWidth: 1,
    borderColor: "rgba(0,0,0,0.5)",
  },
  selectedPromptContainer: {
    borderStyle: "solid",
  },
  promptTextContainer: {
    gap: 10,
    flex: 1,
  },
  nonSelectedText: {
    fontSize: 16,
    opacity: 0.5,
    color: "black",
    fontFamily: "InterTight-Regular",
  },
  selectedText: {
    opacity: 1,
  },
  notEnoughScoops: {
    color: "red",
    fontSize: 16,
    textAlign: "center",
  },
})
