import { StyleSheet } from "react-native"
import { ScreenHeader, ScreenHeaderProps } from "./ScreenHeader"
import { Screen } from "../Themed"
import { Button } from "../Button"
import { BEIGE } from "@/constants/Colors"

export const EmptyState = ({
  title,
  subtitle,
  buttonText,
  buttonIcon,
  backgroundColor = BEIGE,
  style,
  onButtonPress,
}: {
  title: ScreenHeaderProps["title"]
  subtitle: ScreenHeaderProps["subtitle"]
  buttonText: string
  buttonIcon?: any
  backgroundColor?: string
  style?: any
  onButtonPress: () => void
}) => {
  return (
    <Screen style={[styles.container, { backgroundColor }, style]}>
      <ScreenHeader title={title} subtitle={subtitle} />
      <Button
        text={buttonText}
        isTextOnLeft={true}
        iconComponent={buttonIcon}
        onPress={onButtonPress}
        style={{ marginTop: 40 }}
      />
    </Screen>
  )
}

const styles = StyleSheet.create({
  container: {
    paddingTop: 110,
  },
})
