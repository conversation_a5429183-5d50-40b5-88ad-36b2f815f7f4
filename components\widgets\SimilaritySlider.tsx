import { Slider } from "./Slider"
import { Marker } from "../signInOrUp/AgePreferenceSelector"

interface SimilaritySliderProps {
  initialMinValue: number
  initialMaxValue?: number
  disabled?: boolean
  onValuesChange: (values: number[]) => void
}

export const SimilaritySlider = ({
  initialMinValue,
  initialMaxValue,
  disabled,
  onValuesChange,
}: SimilaritySliderProps) => {
  const steps = [0, 25, 50, 75, 100]

  return (
    <Slider
      disabled={disabled}
      min={0}
      max={100}
      step={25}
      showSteps={true}
      showStepLabels={true}
      snapped={true}
      values={[initialMinValue || 0, initialMaxValue || 100]}
      stepsAs={steps.map((s, i) => ({
        index: i,
        stepLabel: s.toString(),
        prefix: "",
        suffix: "%",
      }))}
      minMarkerOverlapStepDistance={2}
      customMarker={({ currentValue }) => (
        <Marker
          disabled={disabled}
          currentValue={currentValue}
          showValue={false}
        />
      )}
      onValuesChangeFinish={onValuesChange}
    />
  )
}
