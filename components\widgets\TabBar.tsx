import { BROWNSTONE } from "@/constants/Colors"
import { StyleSheet, TouchableOpacity, View, Text } from "react-native"

interface TabBarProps {
  tabs: {
    title: string
    selected: boolean
    onPress: () => void
  }[]
}

const TabBar = ({ tabs }: TabBarProps) => (
  <View style={styles.tabSelectors}>
    {tabs.map((tab, index) => (
      <TouchableOpacity key={index} onPress={tab.onPress}>
        <View
          style={{
            ...styles.tab,
            borderBottomWidth: tab.selected ? 3 : 0,
          }}
        >
          <Text
            style={{
              ...styles.tabTitle,
              color: tab.selected ? BROWNSTONE : "black",
            }}
          >
            {tab.title}
          </Text>
        </View>
      </TouchableOpacity>
    ))}
  </View>
)

const styles = StyleSheet.create({
  tabSelectors: {
    backgroundColor: "white",
    flexDirection: "row",
    columnGap: 54,
    justifyContent: "center",
    alignItems: "center",
  },
  tab: {
    paddingVertical: 9,
    width: 60,
    borderBottomColor: BROWNSTONE,
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
  },
  tabTitle: {
    fontFamily: "InterTight-SemiBold",
    fontSize: 13.5,
  },
})

export default TabBar
