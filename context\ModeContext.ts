import { ConnectionMode } from "@/components/signInOrUp/ConnectionModeStep"
import { createContext, useContext } from "react"

export type ActiveConnectionMode = ConnectionMode.Dates | ConnectionMode.Friends
export type ConnectionModeContextValue = {
  activeConnectionMode: ActiveConnectionMode
  setActiveConnectionMode: (activeConnectionMode: ActiveConnectionMode) => void
}

const ConnectionModeContext = createContext<
  ConnectionModeContextValue | undefined
>(undefined)

export const useActiveConnectionMode = () => {
  const contextValue = useContext(ConnectionModeContext)

  if (!contextValue)
    throw new Error(
      "useActiveConnectionMode called outside ConnectionModeContext.Provider",
    )

  return contextValue
}

export default ConnectionModeContext
