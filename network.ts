import axios from "axios"
import * as Sentry from "@sentry/react-native"

interface RequestProps {
  url: string
  method: "GET" | "POST" | "PUT"
  data?: any
  token?: string
  headers?: any
}

export async function request<R>({
  url,
  method,
  data,
  token,
  headers,
}: RequestProps): Promise<R> {
  try {
    const response = await axios.request({
      url,
      method,
      data,
      headers: {
        Authorization: `Bearer ${token}`,
        ...headers,
      },
    })
    return response.data
  } catch (error: unknown) {
    console.warn(`Error requesting URL ${url}`)

    if (axios.isAxiosError(error)) {
      const returnedErrorMessage = error.response?.data?.error
      const errorMessage =
        returnedErrorMessage || error.message || "Unknown axios error"

      if (error.message.includes("Network Error")) {
        console.warn("Network error occurred:", error)
        throw new Error("Network error occurred")
      } else {
        Sentry.captureException(new Error(errorMessage), {
          extra: {
            url,
            method,
            data,
            headers,
            response: error.response?.data,
          },
          fingerprint: [errorMessage, url, method],
        })
        if (returnedErrorMessage) {
          throw new Error(returnedErrorMessage)
        } else if (error.response?.data) {
          throw error.response.data
        } else {
          throw error
        }
      }
    } else {
      Sentry.captureException(new Error("Unexpected error"), {
        extra: { error, url, method, data, headers },
        fingerprint: ["Unexpected error", url, method],
      })
      throw error
    }
  }
}

export async function trackedRequest<R>(props: RequestProps): Promise<R> {
  return Sentry.startSpan({ name: props.url, op: "http.client" }, async () => {
    return request(props)
  })
}

export async function post<D, R>(
  url: string,
  data: D,
  token?: string,
  headers?: any,
  useTracking = true,
): Promise<R> {
  const props: RequestProps = { url, method: "POST", data, token, headers }
  return useTracking ? trackedRequest<R>(props) : request<R>(props)
}

export async function get<D, R>(
  url: string,
  token?: string,
  pathParams?: D,
): Promise<R> {
  const requestUrl = pathParams
    ? `${url}?${new URLSearchParams(pathParams)}`
    : url
  return trackedRequest<R>({ url: requestUrl, method: "GET", token })
}

export async function put<D, R>(
  url: string,
  data: D,
  token?: string,
  headers?: any,
): Promise<R> {
  return trackedRequest<R>({ url, method: "PUT", data, token, headers })
}
