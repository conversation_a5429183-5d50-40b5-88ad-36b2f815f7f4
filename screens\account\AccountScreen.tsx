import {
  StyleSheet,
  Text,
  View,
  ScrollView,
  TouchableOpacity,
} from "react-native"
import { useSession } from "@/ctx"
import { Screen } from "@/components/Themed"
import { Button } from "@/components/Button"
import { useEffect, useState } from "react"
import { useIsFocused } from "@react-navigation/native"
import { Loader } from "@/components/widgets/Loader"
import { ProfileItem, ProfileList } from "@/components/settings/ProfileItems"
import { router } from "expo-router"
import { ConnectionMode } from "@/components/signInOrUp/ConnectionModeStep"
import ShareIcon from "@/components/icons/settings/ShareIcon"
import MatchPreferencesIcon from "@/components/icons/settings/MatchPreferencesIcon"
import SettingsIcon from "@/components/Tab/SettingsIcon"
import MatchesIcon from "@/components/Tab/MatchesIcon"
import Badge from "@/components/profile/Badge"
import { useFeatureFlag } from "posthog-react-native"
import {
  matchWithFriendsPath,
  profilePath,
  qrCodePath,
} from "@/utils/deepLinks"
import { isUserWithProfile, Session } from "@/types/user"
import { Avatar } from "@/components/widgets/Avatar"
import { Image } from "expo-image"

const EXPO_PUBLIC_ENABLE_CONTACT_SYNC =
  process.env.EXPO_PUBLIC_ENABLE_CONTACT_SYNC === "true"

export const AccountScreen = () => {
  const [isLoading, setIsLoading] = useState(true)
  const { session, refreshSession } = useSession()
  const isFocused = useIsFocused()
  const showBadge = useFeatureFlag("levels")

  useEffect(() => {
    if (isFocused && session) {
      setIsLoading(true)
      refreshSession(session.token)
      setIsLoading(false)
    }
  }, [isFocused])

  if (!session || isLoading) {
    return <Loader />
  }

  return <AccountScreen_ user={session.user} showBadge={!!showBadge} />
}

interface AccountScreenProps_ {
  user: Session["user"]
  showBadge: boolean
}

export const AccountScreen_ = ({ user, showBadge }: AccountScreenProps_) => {
  let profileItems: ProfileItem[] = []

  if (isUserWithProfile(user))
    profileItems.push({
      iconComponent: MatchPreferencesIcon,
      title: "Match preferences",
      href: "/(app)/account/match-preferences",
    })

  profileItems = [
    ...profileItems,
    {
      iconComponent: SettingsIcon,
      title: "General settings",
      href: "/(app)/account/settings",
    },
    {
      iconComponent: ShareIcon,
      title: "Share with friends",
      href: "/(app)/account/share-with-friends",
    },
    {
      iconComponent: MatchesIcon,
      title: isUserWithProfile(user)
        ? "Match with friends"
        : "Connect with friends",
      href: matchWithFriendsPath,
      disabled: isUserWithProfile(user) && !user.friendsModeIsActivated,
    },
  ]

  return (
    <Screen style={styles.container}>
      <ScrollView>
        <View style={styles.profileContainer}>
          <TouchableOpacity
            onPress={() =>
              router.push({
                pathname: qrCodePath,
                params: { userName: user.firstName },
              })
            }
          >
            <Avatar user={user} size={160} />

            <TouchableOpacity
              style={styles.qrCodeIconContainer}
              onPress={() => router.push({ pathname: qrCodePath })}
            >
              <Image
                source={require("../../assets/images/qr-code.png")}
                style={styles.qrCodeIconStyle}
              />
            </TouchableOpacity>
          </TouchableOpacity>
          <Text style={styles.profileName}>{user.firstName}</Text>
          {showBadge ? (
            <View
              style={[
                styles.badgeContainer,
                user.level ? { marginTop: -15 } : { marginTop: -5 },
              ]}
            >
              <Badge variant="badge" points={user.points} />
            </View>
          ) : null}

          {user.isNewsOnly ? (
            <Button
              text="Edit InPress News profile"
              onPress={() => router.push({ pathname: profilePath })}
              iconSource={require("@/components/signInOrUp/assets/pencil-line.png")}
              iconTintColor={"white"}
              isTextOnLeft
            />
          ) : null}
        </View>
        {isUserWithProfile(user) && (
          <View style={styles.editProfileContainer}>
            <Button
              text="Edit dates profile"
              onPress={() =>
                router.push({
                  pathname: profilePath,
                  params: { connectionMode: ConnectionMode.Dates },
                })
              }
              disabled={!user.datesModeIsActivated}
            />
            <Button
              text="Edit friends profile"
              style={styles.secondaryButton}
              textStyle={{ color: "black" }}
              onPress={() =>
                router.push({
                  pathname: profilePath,
                  params: { connectionMode: ConnectionMode.Friends },
                })
              }
              disabled={!user.friendsModeIsActivated}
            />
          </View>
        )}
        <View style={{ height: 20 }} />
        <ProfileList items={profileItems} />
      </ScrollView>
    </Screen>
  )
}

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 10,
    gap: 24,
  },
  secondaryButton: {
    backgroundColor: "transparent",
    borderWidth: 1,
    borderColor: "black",
  },
  profileContainer: {
    marginVertical: 28,
    justifyContent: "center",
    alignItems: "center",
    gap: 16,
  },
  editProfileContainer: {
    gap: 10,
    width: "100%",
  },
  profileName: {
    fontFamily: "InterTight-SemiBold",
    fontSize: 24,
  },
  qrCodeIconContainer: {
    height: 45,
    width: 45,
    backgroundColor: "white",
    borderRadius: 50,
    padding: 15,
    justifyContent: "center",
    alignItems: "center",
    borderColor: "black",
    borderWidth: 1,
    position: "absolute",
    right: 0,
    bottom: 0,
  },
  qrCodeIconStyle: {
    height: 25,
    width: 25,
    resizeMode: "contain",
  },
  badgeContainer: {
    flexDirection: "column",
    justifyContent: "center",
    alignItems: "center",
    gap: 16,
  },
  badgeText: {
    color: "black",
    fontSize: 16,
    textAlign: "center",
    textDecorationLine: "underline",
  },
})
