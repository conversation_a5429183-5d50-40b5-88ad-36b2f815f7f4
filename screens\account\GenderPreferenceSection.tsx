import { CheckboxRow } from "@/components/CheckboxRow"
import {
  GenderPrefs,
  genderPrefOptions,
} from "@/components/signInOrUp/GenderPrefStep"
import { SettingsSection } from "./SettingsSection"

export const GenderPreferenceSection = ({
  initialGenders,
  onChange,
}: {
  initialGenders: GenderPrefs
  onChange: (genders: GenderPrefs) => void
}) => {
  return (
    <SettingsSection
      title="Gender preferences"
      subtitle="Who do you want to be matched with? Select all that apply."
    >
      <CheckboxRow
        options={genderPrefOptions}
        initialValues={initialGenders}
        minChecked={1}
        onChange={onChange}
      />
    </SettingsSection>
  )
}
