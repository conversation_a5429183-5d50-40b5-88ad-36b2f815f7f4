import {
  BoldText,
  <PERSON>er<PERSON>ex<PERSON>,
  ItalicHeaderText,
  LargeText,
} from "@/components/StyledText"
import { EmptyState } from "@/components/widgets/EmptyState"
import { ACTIVATE_SOCIAL_PROPS } from "../leads/LeadsTeaser"

export const MatchWithFriendsTeaser = () => {
  return (
    <EmptyState
      title={
        <HeaderText>
          More <ItalicHeaderText>in</ItalicHeaderText> the know than your
          friends?
        </HeaderText>
      }
      subtitle={
        <LargeText>
          Prove it! With <BoldText>InPress Social</BoldText>, send friend
          requests to see your compatibility, shared interests... and use
          InScore Leaderboards to prove you're the most knowledgeable of your
          friends.
        </LargeText>
      }
      {...ACTIVATE_SOCIAL_PROPS}
    />
  )
}
