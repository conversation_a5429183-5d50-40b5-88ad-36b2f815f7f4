import { useMemo, useState } from "react"
import { SettingsSection } from "./SettingsSection"
import {
  View,
  StyleSheet,
  Platform,
  Pressable,
  Modal,
  SafeAreaView,
} from "react-native"
import Colors, { BROWNSTONE } from "@/constants/Colors"
import Map from "@/components/Map"
import { Text } from "@/components/Themed"
import { Ionicons } from "@expo/vector-icons"
import { Button } from "@/components/Button"
import { LatLng } from "react-native-maps"
import { defaultLatitudeDelta, defaultLongitudeDelta } from "@/utils/location"

interface SetLocationSectionProps {
  defaultLocation: LatLng
  onSaveLocation: (location: LatLng) => Promise<void>
}

export const SetLocationSection = ({
  defaultLocation,
  onSaveLocation,
}: SetLocationSectionProps) => {
  const [location, setLocation] = useState<LatLng>(defaultLocation)
  const [modalIsOpen, setModalIsOpen] = useState(false)

  const handleSaveLocation = async () => {
    setModalIsOpen(false)
    await onSaveLocation(location)
  }

  const mapModal = useMemo(
    () => (
      <Modal visible={modalIsOpen}>
        <View style={styles.modalContainer}>
          <SafeAreaView style={styles.container}>
            <Pressable
              onPress={() => setModalIsOpen(false)}
              style={styles.headerContainer}
            >
              <Ionicons name="arrow-back" size={24} color="black" />
            </Pressable>
            <Text style={styles.headerText}>Update location</Text>
          </SafeAreaView>
          <View style={styles.mapTopContainer}>
            <View style={styles.mapContainer}>
              <Map
                defaultLocation={location}
                liteMode={false}
                onLocationChange={(location) => setLocation(location)}
              />
              <Button
                text="Confirm"
                onPress={() => handleSaveLocation()}
                style={styles.saveButton}
                textStyle={{ color: "black" }}
              />
            </View>
          </View>
        </View>
      </Modal>
    ),
    [modalIsOpen, location],
  )

  return (
    <>
      <View style={styles.sectionContainer}>
        <Pressable style={styles.map} onPress={() => setModalIsOpen(true)}>
          <Map
            defaultLocation={location}
            defaultDeltas={{
              latitudeDelta: defaultLatitudeDelta / 2,
              longitudeDelta: defaultLongitudeDelta / 2,
            }}
            liteMode={true}
          />
        </Pressable>
      </View>
      {mapModal}
    </>
  )
}

const styles = StyleSheet.create({
  sectionContainer: {
    width: "95%",
  },
  map: {
    height: 100,
    width: "100%",
    borderRadius: 10,
    overflow: "hidden",
  },
  mapTopContainer: {
    flex: 1,
    padding: 10,
  },
  mapContainer: {
    marginTop: 30,
  },
  saveButton: {
    backgroundColor: "white",
    width: "100%",
    marginTop: 20,
  },
  headerText: {
    fontFamily: "InterTight-SemiBold",
    fontSize: 22,
    marginLeft: 10,
    top: 5,
  },
  container: {
    flexDirection: "row",
    alignItems: "center",
  },
  modalContainer: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  headerContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginLeft: 10,
    marginTop: 10,
  },
  locationText: {
    backgroundColor: BROWNSTONE,
    paddingHorizontal: 20,
    padding: 5,
    borderRadius: 30,
  },
  markerContainer: {
    height: 40,
    alignSelf: "center",
  },
  locationIcon: {
    position: "absolute",
    top: Platform.OS == "android" ? 25 : 0,
    alignSelf: "center",
    tintColor: BROWNSTONE,
    resizeMode: "contain",
    height: 15,
    width: 15,
  },
})
