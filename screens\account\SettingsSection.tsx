import { View, StyleSheet } from "react-native"
import { Text } from "@/components/Themed"

interface SettingsSectionProps {
  title: string
  subtitle?: string
  children: React.ReactNode
}

export const SettingsSection = ({
  title,
  subtitle,
  children,
}: SettingsSectionProps) => {
  return (
    <View>
      <Text style={styles.title}>{title}</Text>
      {subtitle && <Text style={styles.subtitle}>{subtitle}</Text>}
      <View style={styles.children}>{children}</View>
    </View>
  )
}

const styles = StyleSheet.create({
  title: {
    fontWeight: "600",
    fontFamily: "InterTight-SemiBold",
  },
  subtitle: {
    marginTop: 8,
    fontSize: 12,
    color: "gray",
  },
  children: {
    marginTop: 16,
    alignItems: "center",
  },
})
