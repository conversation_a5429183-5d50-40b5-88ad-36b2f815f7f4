import { Text } from "@/components/Themed"
import { SettingsSection } from "./SettingsSection"
import { SimilaritySlider } from "@/components/widgets/SimilaritySlider"
import { StyleSheet } from "react-native"

interface SimilarityPreferenceSectionProps {
  minSimilarity: number
  maxSimilarity: number
  disabled?: boolean
  onChange: (values: number[]) => void
}

export const SimilarityPreferenceSection = ({
  minSimilarity,
  maxSimilarity,
  disabled = false,
  onChange,
}: SimilarityPreferenceSectionProps) => {
  return (
    <SettingsSection
      title="Match similarity range"
      subtitle="Choose the percentage range for who you match with. A narrow range can limit the number of matches you get."
    >
      <SimilaritySlider
        initialMinValue={minSimilarity}
        initialMaxValue={maxSimilarity}
        disabled={disabled}
        onValuesChange={onChange}
      />
      {disabled && (
        <Text style={styles.disabledDisclaimer}>
          The similarity slider will become available after you have spent some
          time using the app and interacting with our features.
        </Text>
      )}
    </SettingsSection>
  )
}

const styles = StyleSheet.create({
  disabledDisclaimer: {
    marginTop: 25,
    marginBottom: -10,
    color: "gray",
    fontSize: 12,
  },
})
