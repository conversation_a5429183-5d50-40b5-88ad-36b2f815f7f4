import { ActivateParams } from "@/app/(signInOrUp)/activate-mode"
import {
  BoldText,
  LargeText,
  Paragraph,
  Paragraphs,
} from "@/components/StyledText"
import { EmptyState } from "@/components/widgets/EmptyState"
import { activateModePath } from "@/utils/deepLinks"
import { pushWithParams } from "@/utils/localParams"
import { MaterialIcons } from "@expo/vector-icons"

export const ACTIVATE_SOCIAL_PROPS = {
  buttonText: "Activate InPress Social",
  buttonIcon: <MaterialIcons name="group-add" size={21} color="white" />,
  style: { marginTop: -60 },
  onButtonPress: () => {
    pushWithParams<ActivateParams>({
      pathname: activateModePath,
      params: { isActivatingSocialStr: "true" },
    })
  },
}

export const LeadsTeaser = () => {
  return (
    <EmptyState
      title="News ratings connect, too!"
      subtitle={
        <Paragraphs>
          <Paragraph>
            <LargeText>
              Your news ratings can also help you find your perfect match - for
              Friends or Dating!
            </LargeText>
          </Paragraph>
          <Paragraph>
            <LargeText>
              <BoldText>Leads</BoldText> are other users who share your
              news-informed interests! If you both swipe right, it's a{" "}
              <BoldText>Match</BoldText>!
            </LargeText>
          </Paragraph>
          <Paragraph>
            <LargeText>
              Activate <BoldText>InPress Social</BoldText> to see who's on your
              wavelength:
            </LargeText>
          </Paragraph>
        </Paragraphs>
      }
      {...ACTIVATE_SOCIAL_PROPS}
    />
  )
}
