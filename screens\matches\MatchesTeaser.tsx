import {
  BoldText,
  LargeText,
  Paragraph,
  Paragraphs,
} from "@/components/StyledText"
import { EmptyState } from "@/components/widgets/EmptyState"
import { ACTIVATE_SOCIAL_PROPS } from "../leads/LeadsTeaser"

export const MatchesTeaser = () => {
  return (
    <EmptyState
      title="Intellectual Matches await"
      subtitle={
        <Paragraphs>
          <Paragraph>
            <LargeText>
              Leads becomes <BoldText>Matches</BoldText> when both users
              indicate interest in each other!
            </LargeText>
          </Paragraph>
          <Paragraph>
            <LargeText>
              Curious to see who's a great Match? Activate{" "}
              <BoldText>InPress Social</BoldText> to find out.
            </LargeText>
          </Paragraph>
        </Paragraphs>
      }
      {...ACTIVATE_SOCIAL_PROPS}
    />
  )
}
