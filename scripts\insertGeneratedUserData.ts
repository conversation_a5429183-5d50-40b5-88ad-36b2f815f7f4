import fs from "fs"
import path from "path"
import csvParser from "csv-parser"
import dotenv from "dotenv"
dotenv.config()

interface User {
  id: number
  name: string
  gender: string
}

interface ProfilePic {
  path: string
  gender: string
}

interface Survey {
  id: number
  topics: string
  article_id: string
  user_id: string
  care: string
  feel: string
}

const knex = require("knex")({
  client: "mysql2",
  connection: {
    host: process.env.DB_HOST,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
  },
})

async function insert({
  record,
  table,
}: {
  record: any
  table: string
}): Promise<number> {
  console.log("Inserting record into table:", table)
  const [insertedId] = await knex(table)
    .insert(record)
    .on("error", (error: any) => {
      throw new Error(`Error inserting record into table ${table}: ${error}`)
    })
  console.log("Inserted record ID:", insertedId)
  return insertedId
}

async function insertGatheredData({
  users,
  profilePics: profilePics,
  surveys,
}: {
  users: User[]
  profilePics: ProfilePic[]
  surveys: Survey[]
}) {
  for await (const [userIndex, user] of users.entries()) {
    const userId = await insert({ record: user, table: "users" })
    const genderMatchingPics = profilePics.filter(
      (pic) => pic.gender === user.gender,
    )
    const profilePicIndex = userIndex % genderMatchingPics.length
    const surveyUserId = userIndex % surveys.length
    await insert({
      record: {
        path: genderMatchingPics[profilePicIndex].path,
        user_id: userId,
      },
      table: "images",
    })
    const surveysMatchingUser = surveys.filter(
      (survey) => parseInt(survey.user_id) === surveyUserId,
    )
    for await (const survey of surveysMatchingUser) {
      await insert({
        record: {
          ...survey,
          feel: JSON.stringify(survey.feel),
          user_id: userId,
        },
        table: "surveys",
      })
    }
  }
}

function csvToObject<T>(filePath: string): Promise<T[]> {
  return new Promise((resolve, reject) => {
    const rows: T[] = []
    fs.createReadStream(filePath)
      .pipe(csvParser())
      .on("data", (row: T) => {
        rows.push(row)
      })
      .on("end", () => {
        resolve(rows as T[])
      })
      .on("error", (err: any) => {
        reject(err)
      })
  })
}

async function gatherGeneratedDataAndInsert() {
  const dataDir = path.resolve(__dirname, "../generatedData")
  const users = require(path.resolve(dataDir, "generatedUsers.json"))

  const malePicFiles = fs
    .readdirSync(path.resolve(dataDir, "generatedProfilePics/male"))
    .map((filename) => ({
      filename,
      gender: "male",
    }))

  const femalePicFiles = fs
    .readdirSync(path.resolve(dataDir, "generatedProfilePics/female"))
    .map((filename) => ({
      filename,
      gender: "female",
    }))

  const profilePics = [malePicFiles, femalePicFiles]
    .flat()
    .filter(({ filename }) => filename !== ".DS_Store")
    .map(({ filename, gender }) => ({
      path: `images/generated_profile_pics/${gender}/${filename}`,
      gender,
    }))

  const surveys = await csvToObject<Survey>(
    path.resolve(dataDir, "surveys.csv"),
  )

  console.log(surveys)
  await insertGatheredData({ users, profilePics, surveys })
  await knex.destroy()
  console.log("Done")
}

gatherGeneratedDataAndInsert()
