import fs from "fs"
import path from "path"
import sharp from "sharp"

type ResizeInstruction = {
  imagePath: string
  dimension?: "width" | "height"
  ratio?: number
}

const inputDir = "./inputImages"
const outputDir = "./outputImages"
const instructionsPath = "./adjustments.json"

const loadInstructions = (): ResizeInstruction[] => {
  const raw = fs.readFileSync(instructionsPath, "utf-8")
  return JSON.parse(raw) as ResizeInstruction[]
}

const processImage = async (instruction: ResizeInstruction): Promise<void> => {
  const { imagePath } = instruction
  let { dimension, ratio } = instruction
  const inputPath = path.join(inputDir, `images_${path.basename(imagePath)}`)
  const outputPath = path.join(outputDir, path.basename(imagePath))

  const image = sharp(inputPath)
  const metadata = await image.metadata()

  if (!metadata.width || !metadata.height) {
    throw new Error(`Missing dimensions for ${imagePath}`)
  }

  let newWidth = metadata.width
  let newHeight = metadata.height

  if (!ratio) {
    const typicalDistortionRatio = 1.34
    ratio = typicalDistortionRatio
  }

  if (!dimension) {
    dimension = "width"
  }

  console.log(
    `Resizing ${imagePath} (${metadata.width}x${metadata.height}) with ratio ${ratio} for ${dimension}`,
  )

  if (dimension === "width") {
    newWidth = Math.round(metadata.width * ratio)
  } else if (dimension === "height") {
    newHeight = Math.round(metadata.height * ratio)
  }

  await image.resize(newWidth, newHeight, { fit: "fill" }).toFile(outputPath)

  console.log(
    `✅ ${imagePath} resized from ${metadata.width}x${metadata.height} to ${newWidth}x${newHeight} (using dimension ${dimension} and ratio ${ratio})`,
  )
}

const run = async () => {
  const instructions = loadInstructions()
  for (const instruction of instructions) {
    try {
      await processImage(instruction)
    } catch (err) {
      console.error(`❌ Failed to process ${instruction.imagePath}:`, err)
    }
  }
}

run()
