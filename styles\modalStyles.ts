import { StyleSheet } from "react-native"

export const modalStyles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.5)",
  },
  modalContent: {
    width: "90%",
    backgroundColor: "white",
    borderRadius: 10,
    padding: 20,
    alignItems: "center",
  },
  scrollView: {
    justifyContent: "center",
    paddingVertical: 3,
    paddingBottom: 20,
  },
  header: {
    fontSize: 24,
    fontFamily: "InterTight-SemiBold",
    marginBottom: 10,
  },
  subHeader: {
    fontFamily: "InterTight-Regular",
    fontSize: 18,
    marginBottom: 10,
  },
  reasonsContainer: {
    flexDirection: "row",
    justifyContent: "center",
    marginBottom: 20,
  },
  reasonButton: {
    backgroundColor: "#f0f0f0",
    borderRadius: 20,
    paddingVertical: 8,
    paddingHorizontal: 15,
    margin: 5,
  },
  selectedReason: {
    backgroundColor: "#e0e0e0",
  },
  reasonText: {
    fontSize: 14,
    color: "black",
    fontFamily: "InterTight-Regular",
  },
  selectedReasonText: {
    color: "red",
  },
  reasonLabel: {
    fontSize: 16,
    fontFamily: "InterTight-SemiBold",
    alignSelf: "flex-start",
    marginBottom: 5,
  },
  input: {
    justifyContent: "flex-end",
    paddingVertical: 10,
    paddingTop: 10,
    textAlignVertical: "top",
  },
  textInput: {
    borderWidth: 1,
    borderRadius: 10,
    marginBottom: 20,
    height: 100,
  },
  helperText: {
    fontSize: 14,
    color: "gray",
    alignSelf: "flex-start",
    marginBottom: 10,
    fontFamily: "InterTight-Regular",
  },
  button: {
    width: "100%",
    backgroundColor: "black",
    borderRadius: 10,
    paddingVertical: 15,
    alignItems: "center",
  },
  cancelButton: {
    backgroundColor: "white",
    marginTop: 12,
    borderColor: "black",
    borderWidth: 1,
  },
  buttonText: {
    color: "white",
    fontSize: 16,
    fontWeight: "bold",
    fontFamily: "InterTight-SemiBold",
  },
  cancelButtonText: {
    color: "black",
    fontSize: 16,
    fontWeight: "bold",
    fontFamily: "InterTight-SemiBold",
  },
})
