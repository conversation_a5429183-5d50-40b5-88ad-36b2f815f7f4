import { submitSurvey } from "@/apiQueries/apiQueries"
import { FinishedSurvey } from "@/components/ratings/ArticleSurvey"
import { getSession } from "./apiQueries/apiQueryTests"

it("submits a survey", async () => {
  const session = await getSession()
  const survey: FinishedSurvey = {
    feelings: ["TEST"],
    importanceRating: 1,
    interestRating: 1,
  }

  if (!session) {
    throw new Error("No session")
  }

  await submitSurvey({ token: session.token, articleId: 1, survey })
})
