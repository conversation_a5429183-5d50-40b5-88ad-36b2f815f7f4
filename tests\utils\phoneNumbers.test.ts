import { normalizePhoneNumber } from "@/utils/phoneNumbers"

describe("normalizePhoneNumber", () => {
  it("does nothing with a normalized phone number", () => {
    const phoneNumber = "+1234567890"
    const result = normalizePhoneNumber(phoneNumber)
    expect(result).toBe("+1234567890")
  })
  it("normalizes a phone number without the +", () => {
    const phoneNumber = "12223334444"
    const result = normalizePhoneNumber(phoneNumber)
    expect(result).toBe("+12223334444")
  })
  it("normalizes a phone number with spaces and parentheses", () => {
    const phoneNumber = "+****************"
    const result = normalizePhoneNumber(phoneNumber)
    expect(result).toBe("+11234567890")
  })
  it("normalizes a phone number with periods", () => {
    const phoneNumber = "******.456.7890"
    const result = normalizePhoneNumber(phoneNumber)
    expect(result).toBe("+11234567890")
  })
  it("normalizes a phone number without the country code", () => {
    const phoneNumber = "2223334444"
    const result = normalizePhoneNumber(phoneNumber)
    expect(result).toBe("+12223334444")
  })
})
