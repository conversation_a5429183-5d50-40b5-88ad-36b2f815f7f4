import * as Sentry from "@sentry/react-native"
import * as FileSystem from "expo-file-system"

export const makeLocalUri = (url: string): string => {
  const fileName = url.split("/").pop()
  const fileUri = `${FileSystem.cacheDirectory}${fileName}`
  return fileUri
}

export const cacheComponent = async (url: string): Promise<string> => {
  try {
    const fileUri = makeLocalUri(url)

    const fileInfo = await FileSystem.getInfoAsync(fileUri)

    if (fileInfo.exists) {
      FileSystem.downloadAsync(url, fileUri)
    } else {
      const result = await FileSystem.downloadAsync(url, fileUri)
      if (result.status !== 200) {
        await FileSystem.deleteAsync(fileUri, { idempotent: true })
        throw new Error(`Failed to download file: ${url}`)
      }
    }

    return fileUri
  } catch (error) {
    Sentry.captureException(error, { fingerprint: ["cacheComponent"] })
    console.error(`Failed to cache component: ${url}`, error)
    return url
  }
}
