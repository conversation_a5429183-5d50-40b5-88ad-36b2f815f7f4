import { router } from "expo-router"
import { Platform } from "react-native"

const openAppStore = () => {
  if (Platform.OS === "ios") {
    router.push(
      "https://apps.apple.com/us/app/inpress-informed-connection/id6456752116",
    )
  } else if (Platform.OS === "android") {
    router.push(
      "https://play.google.com/store/apps/details?id=com.scoopt.inpress",
    )
  } else {
    console.error("Unknown platform")
  }
}

export const isImpersonating = process.env.EXPO_PUBLIC_IMPERSONATING === "true"

export { openAppStore }
