import * as ImagePicker from "expo-image-picker"
import { showSystemPermissionsAlert } from "./permissions"
import { ImageManipulator, SaveFormat } from "expo-image-manipulator"

export const requestMediaPermissions = async () => {
  const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync()
  if (status !== "granted") {
    showSystemPermissionsAlert(
      "Permission blocked",
      "Sorry, we need camera roll permissions for this! Please enable it from settings.",
    )
    return false
  } else {
    return true
  }
}

export const pickImage = async (): Promise<string | undefined> => {
  const granted = await requestMediaPermissions()
  if (!granted) return

  let result = await ImagePicker.launchImageLibraryAsync({
    mediaTypes: ImagePicker.MediaTypeOptions.Images,
    allowsMultipleSelection: false,
    quality: 1,
  })

  if (!result.canceled && result.assets && result.assets.length > 0) {
    return result.assets[0].uri
  } else {
    return
  }
}

const getImageDimensions = async (
  uri: string,
): Promise<{ width: number; height: number } | null> => {
  try {
    const context = ImageManipulator.manipulate(uri)
    const image = await context.renderAsync()
    return {
      height: image.height,
      width: image.width,
    }
  } catch (error) {
    console.error("Error getting image dimensions:", error)
    return null
  }
}

export const resizeImage = async (uri: string) => {
  const dimensions = await getImageDimensions(uri)
  if (!dimensions) {
    console.error("Failed to get image dimensions", { uri })
    return uri
  }

  const { width, height } = dimensions

  try {
    const maxDim = 800
    const scale = maxDim / Math.max(width, height)

    const context = ImageManipulator.manipulate(uri)

    context.resize({
      width: Math.round(width * scale),
      height: Math.round(height * scale),
    })

    const image = await context.renderAsync()
    const result = await image.saveAsync({
      format: SaveFormat.JPEG,
      compress: 0.8,
    })

    return result.uri
  } catch (error) {
    console.error("Error resizing image:", {
      error,
      uri,
      dimensions,
    })
    return uri
  }
}
