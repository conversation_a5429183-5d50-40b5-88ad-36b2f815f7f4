import { Alert, Dimensions, Platform } from "react-native"
import MapView, { LatLng } from "react-native-maps"
import * as Location from "expo-location"
import Geocoder from "react-native-geocoding"
import { showSystemPermissionsAlert } from "./permissions"
import AsyncStorage from "@react-native-async-storage/async-storage"
import moment from "moment"
import { LAST_LOC_UPDATE_TIME_KEY } from "./localStorage"

export const Screen = Dimensions.get("screen")
export const ScreenHeight: number = Screen.height
export const Window = Dimensions.get("window")
export const WindowWidth: number = Window.width
export const defaultLatitudeDelta = 0.02686120434926778
export const defaultLongitudeDelta = 0.029223175048842336

export const MapHeight =
  Platform.OS === "android" ? ScreenHeight / 2.8 : ScreenHeight / 3

export const requestLocationPermission = async () => {
  const { status } = await Location.requestForegroundPermissionsAsync()

  if (status !== "granted") {
    showSystemPermissionsAlert(
      "Permission Blocked",
      "We don't have permission to access your location. Please enable it from settings.",
    )
  }

  return status
}

export const showPermissionsAlert = ({
  onConfirm,
}: {
  onConfirm: () => void
}) => {
  Alert.alert(
    "Location Services Disabled",
    "Please enable location services to continue.",
    [
      { text: "Cancel", style: "cancel" },
      {
        text: "Continue",
        onPress: onConfirm,
      },
    ],
  )
}

export const animateCamera = (mapView: MapView | null, position: LatLng) => {
  if (mapView) {
    mapView.animateCamera(
      {
        center: position,
        zoom: 15,
      },
      { duration: 500 },
    )
  }
}

export const getAddressData = async (location: LatLng): Promise<string> => {
  try {
    const response = await Geocoder.from(location.latitude, location.longitude)
    const addressComponents = response.results[0].address_components

    const neighborhoodComponent = addressComponents.find((component) =>
      component.types.includes("neighborhood"),
    )
    const sublocalityComponent = addressComponents.find(
      (component) =>
        component.types.includes("sublocality") ||
        component.types.includes("sublocality_level_1"),
    )

    if (neighborhoodComponent) {
      return neighborhoodComponent.long_name
    } else if (sublocalityComponent) {
      return sublocalityComponent.long_name
    } else {
      return response.results[0].address_components[1].short_name
    }
  } catch (error) {
    console.warn(error)
    throw new Error("Failed to get address component")
  }
}

export const hasRecentlyUpdatedLocation = async (): Promise<boolean> => {
  const lastUpdateTimeStr = await AsyncStorage.getItem(LAST_LOC_UPDATE_TIME_KEY)
  if (!lastUpdateTimeStr) {
    return false
  }

  const lastUpdateTime = moment(lastUpdateTimeStr)
  const currentTime = moment()

  const timeDifferenceMinutes = currentTime.diff(lastUpdateTime, "minutes")
  return timeDifferenceMinutes < 60
}
