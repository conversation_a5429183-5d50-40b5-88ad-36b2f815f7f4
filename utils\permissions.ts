import { Platform, Linking, Alert } from "react-native"

const openSystemSettings = async () => {
  if (Platform.OS === "android") {
    Linking.openSettings()
  } else {
    Linking.openURL("app-settings:")
  }
}

const showSystemPermissionsAlert = (title: string, message: string) => {
  Alert.alert(title, message, [
    { text: "Cancel", style: "cancel" },
    {
      text: "Open Settings",
      onPress: () => openSystemSettings(),
    },
  ])
}

export { openSystemSettings, showSystemPermissionsAlert }
