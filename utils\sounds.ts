import { Audio } from "expo-av"

export interface SoundAsset {
  type: string
  sound: Audio.Sound
  source: any
}

export const levelsSoundAssets: SoundAsset[] = [
  {
    type: "points-gained-1",
    sound: new Audio.Sound(),
    source: require("@/assets/audio/points-gained-1.mp3"),
  },
  {
    type: "points-gained-2",
    sound: new Audio.Sound(),
    source: require("@/assets/audio/points-gained-2.mp3"),
  },
  {
    type: "points-gained-3",
    sound: new Audio.Sound(),
    source: require("@/assets/audio/points-gained-3.mp3"),
  },
  {
    type: "level-up",
    sound: new Audio.Sound(),
    source: require("@/assets/audio/level-up.mp3"),
  },
]
